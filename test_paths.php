<?php
require_once 'vendor/autoload.php';

// Boot Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Path Verification ===\n";

// Current path used by TagNameTagGenerator
echo "Current TagNameTagGenerator path: " . storage_path('app/public/visitor_images/') . "\n";
echo "Directory exists: " . (is_dir(storage_path('app/public/visitor_images/')) ? 'YES' : 'NO') . "\n";

// Correct path using public storage
echo "Correct public storage path: " . public_path('storage/visitor_images/') . "\n";
echo "Directory exists: " . (is_dir(public_path('storage/visitor_images/')) ? 'YES' : 'NO') . "\n";

// Check if both paths point to the same location
echo "Are paths the same? " . (realpath(storage_path('app/public/visitor_images/')) === realpath(public_path('storage/visitor_images/')) ? 'YES' : 'NO') . "\n";

// Test file existence with both paths
$testFile = '1745277698_Kristin_Burke.png';
echo "\nTesting file: $testFile\n";
echo "TagNameTagGenerator path: " . storage_path('app/public/visitor_images/' . $testFile) . "\n";
echo "File exists: " . (file_exists(storage_path('app/public/visitor_images/' . $testFile)) ? 'YES' : 'NO') . "\n";
echo "Public storage path: " . public_path('storage/visitor_images/' . $testFile) . "\n";
echo "File exists: " . (file_exists(public_path('storage/visitor_images/' . $testFile)) ? 'YES' : 'NO') . "\n";
?>
