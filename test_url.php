<?php
require_once 'vendor/autoload.php';

// Boot Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== URL Accessibility Test ===\n";

$problematicFile = "1746249679_<PERSON><PERSON>_empty.png";
$encodedFile = urlencode($problematicFile);

// Test different URL formats
$baseUrl = url('storage/visitor_images/');
$testUrls = [
    "Original" => $baseUrl . '/' . $problematicFile,
    "URL Encoded" => $baseUrl . '/' . $encodedFile,
    "Raw URL" => 'http://127.0.0.1:8000/storage/visitor_images/' . $encodedFile
];

echo "Testing URLs for: $problematicFile\n\n";

foreach($testUrls as $type => $url) {
    echo "$type: $url\n";
    
    // Check if file exists locally
    $localPath = public_path('storage/visitor_images/' . $problematicFile);
    echo "Local file exists: " . (file_exists($localPath) ? 'YES' : 'NO') . "\n";
    
    // Test URL with file_get_contents
    $context = stream_context_create([
        'http' => [
            'method' => 'HEAD',
            'timeout' => 5
        ]
    ]);
    
    $headers = @get_headers($url, 0, $context);
    if ($headers) {
        echo "HTTP Status: " . $headers[0] . "\n";
    } else {
        echo "Failed to get headers\n";
    }
    
    echo "---\n";
}

// Test with a working file for comparison
$workingFile = "1745277698_Kristin_Burke.png";
echo "\nTesting working file for comparison: $workingFile\n";
$workingUrl = $baseUrl . '/' . $workingFile;
echo "URL: $workingUrl\n";
$headers = @get_headers($workingUrl);
if ($headers) {
    echo "HTTP Status: " . $headers[0] . "\n";
} else {
    echo "Failed to get headers\n";
}
?>
