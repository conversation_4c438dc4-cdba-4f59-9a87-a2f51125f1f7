<?php
require_once 'vendor/autoload.php';

// Boot Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Fixing Spaces in Visitor Images ===\n";

// Get all files with spaces
$dir = public_path('storage/visitor_images');
$files = scandir($dir);
$filesWithSpaces = [];

foreach($files as $file) {
    if ($file !== '.' && $file !== '..' && strpos($file, ' ') !== false) {
        $filesWithSpaces[] = $file;
    }
}

echo "Found " . count($filesWithSpaces) . " files with spaces\n\n";

// Process each file
$updated = 0;
foreach($filesWithSpaces as $oldName) {
    $newName = str_replace(' ', '_', $oldName);
    
    echo "Processing: $oldName -> $newName\n";
    
    // Rename the file
    $oldPath = $dir . '/' . $oldName;
    $newPath = $dir . '/' . $newName;
    
    if (rename($oldPath, $newPath)) {
        echo "  ✓ File renamed successfully\n";
        
        // Update database records
        $affected = \App\Models\Visitor::where('image', $oldName)->update(['image' => $newName]);
        if ($affected > 0) {
            echo "  ✓ Updated $affected database record(s)\n";
            $updated++;
        } else {
            echo "  ⚠ No database records updated (file might not be referenced)\n";
        }
    } else {
        echo "  ✗ Failed to rename file\n";
    }
    
    echo "\n";
}

echo "=== Summary ===\n";
echo "Files processed: " . count($filesWithSpaces) . "\n";
echo "Database records updated: $updated\n";
echo "✅ Fix completed!\n";
?>
