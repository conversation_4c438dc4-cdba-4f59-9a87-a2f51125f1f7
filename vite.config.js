import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                'resources/css/app.css',
                'resources/js/app.js',
                'resources/js/otp-input.js',
                'resources/css/visitor-ticket.css',
                'resources/css/visitor-registration.css',
                'resources/css/visitor-login.css',
                'resources/css/visitor-name-tag.css',
                'resources/css/visitor-reset-password.css',
                'resources/css/verify-new-password.css',
            ],
            refresh: true,
        }),
    ],
});
