<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGraduationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('graduations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email');
            $table->string('certificate' );
            $table->string('phone')->nullable();
            $table->string('company')->nullable();
            $table->string('title' )->nullable();
            $table->string('wagroup')->nullable();
            $table->string('facebook' )->nullable();
            $table->string('linkedin')->nullable();
            $table->string('code' );
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('graduations');
    }
}
