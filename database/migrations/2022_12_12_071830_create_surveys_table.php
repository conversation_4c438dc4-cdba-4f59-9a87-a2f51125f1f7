<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSurveysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('surveys'  , function (Blueprint $table) {
              $table->id();
              $table->string('fullname' )->nullable();
              $table->string('mobile' )->nullable();
              $table->string('email' )->nullable();
              $table->string('satisfiedrate' )->nullable();
              $table->string('recommentconf' )->nullable();
              $table->string('panelspeechrate' )->nullable();
              $table->string('bestTopics' )->nullable();
              $table->string('nextTopic' )->nullable();
              $table->string('attend_workshops' )->nullable();
              $table->string('workshops')->nullable();
              $table->string('afternoonrate' )->nullable();
              $table->string('eveningrate' )->nullable();
              $table->string('conference_comments')->nullable();
              $table->string('afpbefore')->nullable();
              $table->string('afpfrom')->nullable();
              $table->string('membership')->nullable();
              $table->string('join_certificate')->nullable();
              $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('surveys');
    }
}
