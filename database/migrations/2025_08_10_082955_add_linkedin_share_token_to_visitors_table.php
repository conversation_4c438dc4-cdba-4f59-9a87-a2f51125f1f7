<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('visitors', function (Blueprint $table) {
            $table->string('linkedin_share_token', 64)->nullable()->unique()->after('posted_at');
            $table->timestamp('linkedin_share_token_expires_at')->nullable()->after('linkedin_share_token');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('visitors', function (Blueprint $table) {
            $table->dropColumn(['linkedin_share_token', 'linkedin_share_token_expires_at']);
        });
    }
};
