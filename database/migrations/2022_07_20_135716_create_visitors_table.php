<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVisitorsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('visitors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->string('jop_title')->nullable();
            $table->string('phone')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->string('company')->nullable();
            $table->string('company_category')->nullable();
            $table->string('linkedin')->nullable();
            $table->string('facebook')->nullable();
            $table->string('image')->nullable();
            $table->string('name_tag')->nullable();
            $table->string('name_tag_status')->nullable();
            $table->boolean('approved')->default(0);
            $table->string('password')->nullable();
            $table->string('post_status')->nullable();
            $table->string('post_approval')->nullable();
            $table->enum('type',['visitor','speaker'])->default('visitor');
            $table->string('qr_code')->nullable();
            $table->string('bio')->nullable();
            $table->boolean('attend')->default(0);
            $table->timestamp('attended_at')->nullable();
            $table->timestamp('last_sign_in')->nullable();
            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('visitors');
    }
}
