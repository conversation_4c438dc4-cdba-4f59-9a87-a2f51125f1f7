<?php
require_once 'vendor/autoload.php';

use App\Models\Visitor;

// Boot Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Visitor Images Diagnostic ===\n";

// Get all visitors with images
$visitors = Visitor::whereNotNull('image')->where('image', '!=', '')->get();
echo "Total visitors with images: " . $visitors->count() . "\n\n";

$publicPath = public_path('storage/visitor_images');
echo "Checking images in: $publicPath\n\n";

$missing = [];
$existing = [];

foreach($visitors as $visitor) {
    $imagePath = $publicPath . '/' . $visitor->image;
    if (file_exists($imagePath)) {
        $existing[] = $visitor->id . ': ' . $visitor->image;
    } else {
        $missing[] = $visitor->id . ': ' . $visitor->image;
    }
}

echo "EXISTING IMAGES (" . count($existing) . "):\n";
foreach(array_slice($existing, 0, 10) as $item) {
    echo "  $item\n";
}
if (count($existing) > 10) {
    echo "  ... and " . (count($existing) - 10) . " more\n";
}

echo "\nMISSING IMAGES (" . count($missing) . "):\n";
foreach($missing as $item) {
    echo "  $item\n";
}

echo "\n=== File System Check ===\n";
echo "Directory exists: " . (is_dir($publicPath) ? 'YES' : 'NO') . "\n";
if (is_dir($publicPath)) {
    $files = scandir($publicPath);
    $imageFiles = array_filter($files, function($file) {
        return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
    });
    echo "Total image files in directory: " . count($imageFiles) . "\n";
    echo "Sample files:\n";
    foreach(array_slice($imageFiles, 0, 5) as $file) {
        echo "  $file\n";
    }
}

echo "\n=== URL Generation Test ===\n";
if ($visitors->isNotEmpty()) {
    $visitor = $visitors->first();
    $url = asset('storage/visitor_images/' . $visitor->image);
    echo "Sample URL: $url\n";
    echo "File exists: " . (file_exists($publicPath . '/' . $visitor->image) ? 'YES' : 'NO') . "\n";
}
?>
