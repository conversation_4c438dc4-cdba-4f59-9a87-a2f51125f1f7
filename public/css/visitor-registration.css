/* Visitor Registration CSS */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

.text-primary {
    color: #0e2a47 !important;
}

.btn-primary {
    background-color: #0e2a47;
    border-color: #0e2a47;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #0a1f36;
    border-color: #0a1f36;
}

.btn-outline-primary {
    color: #0e2a47;
    border-color: #0e2a47;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: #0e2a47;
    color: white;
}

.btn-outline-primary.active {
    background-color: #f8f9fa;
    color: #0e2a47;
    border-color: #0e2a47;
}

/* Progress Steps */
.progress-container {
    margin-bottom: 2rem;
}

.progress-step {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.progress-circle {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 8px;
    border: 2px solid #e9ecef;
}

.progress-step.active .progress-circle {
    background-color: #0e2a47;
    color: white;
    border-color: #0e2a47;
}

.progress-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

.progress-step.active .progress-text {
    color: #0e2a47;
    font-weight: 600;
}

.progress-line-container {
    height: 2px;
    z-index: 0;
}

.progress-line {
    height: 2px;
    background-color: #e9ecef;
    position: relative;
}

.progress-line-inner {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background-color: #0e2a47;
    transition: width 0.3s ease;
}

.z-index-1 {
    z-index: 1;
}

/* Form Styles */
.form-control, .form-select {
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0e2a47;
    box-shadow: 0 0 0 0.25rem rgba(14, 42, 71, 0.25);
}

.input-group-text {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
}

/* Phone Input Styling */
.phone-flag {
    width: 24px;
    height: 18px;
    display: inline-block;
    background-size: cover;
    vertical-align: middle;
}

.phone-code {
    font-size: 14px;
    font-weight: 500;
}

/* Profile Photo */
.profile-preview-container {
    width: 100px;
    height: 100px;
    overflow: hidden;
}

.profile-preview, .profile-placeholder {
    width: 100px;
    height: 100px;
    object-fit: cover;
}

.profile-placeholder {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
}

/* Password Toggle */
.password-toggle {
    cursor: pointer;
    color: #6c757d;
    font-size: 14px;
}

.password-toggle:hover {
    color: #0e2a47;
}

/* Card Styling */
.card {
    border-radius: 10px;
    border: 1px solid #eaeaea;
}

/* Footer Links */
a {
    color: #0e2a47;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Responsive styles for auth tabs */
@media (max-width: 768px) {
    .auth-tabs-container {
        width: 100%;
    }
    
    .auth-tabs-container .row {
        width: 100% !important;
        margin: 0 auto;
    }
    
    .auth-tabs-container .btn {
        padding: 14px 4px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .auth-tabs-container .btn {
        padding: 12px 4px;
        font-size: 13px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
