/* Custom Ticket Styles */
body {
    background-color: #f5f8fa;
    font-family: "Gotham Narrow", Arial, sans-serif;
}

.ticket-wrapper {
    max-width: 800px;
    margin: 50px auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.ticket-card {
    width: 100%;
    background-color: white;
    border: 2px solid white;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    padding: 40px 30px;
    margin-bottom: 20px;
    position: relative;
    text-align: center;
}

.ticket-content {
    padding: 10px;
}

.ticket-title {
    margin-bottom: 25px;
}

.my-text {
    color: #007DB6;
    font-size: 28px;
    font-family: "Gotham Narrow", sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    word-wrap: break-word;
}

.ticket-text {
    color: #333;
    font-size: 28px;
    font-family: "Gotham Narrow", sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    word-wrap: break-word;
}

.ticket-message {
    margin-bottom: 20px;
}

.dear-text {
    color: #007DB6;
    font-size: 16px;
    font-family: "Gotham Narrow", sans-serif;
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
}

.details-text {
    color: #007DB6;
    font-size: 16px;
    font-family: "Gotham Narrow", sans-serif;
    font-weight: 600;
    line-height: 24px;
    word-wrap: break-word;
}

.ticket-details {
    color: #555;
    font-size: 16px;
    font-family: "Gotham Narrow", sans-serif;
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
    margin-bottom: 12px;
}

.mb-4 {
    margin-bottom: 2rem;
}

.agenda-button-container {
    margin-top: 30px;
    text-align: center;
    width: 100%;
}

.agenda-button {
    display: inline-block;
    background-color: #102649;
    color: white;
    font-size: 16px;
    font-family: "Gotham Narrow", sans-serif;
    font-weight: 500;
    word-wrap: break-word;
    padding: 14px 40px;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.agenda-button:hover {
    background-color: #0a1a33;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

/* Responsive design */
@media (max-width: 768px) {
    .ticket-wrapper {
        margin: 20px;
        max-width: none;
    }

    .ticket-card {
        padding: 30px 20px;
    }

    .ticket-title {
        font-size: 28px;
    }

    .agenda-button {
        padding: 12px 30px;
        font-size: 14px;
    }
}

/* Add subtle animation */
.ticket-card {
    animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
