# Cairo Beacon Event Management System

## Project Overview

The Cairo Beacon Event Management System is a comprehensive Laravel-based web application designed to facilitate the management of professional events, conferences, and forums in Cairo. Built with Laravel Livewire for dynamic, reactive user interfaces, the platform connects event organizers, sponsors, speakers, and attendees through a seamless digital experience.

## Core Features

### Visitor Registration & Authentication
- **Multi-step Registration Process** (VisitorRegister.php)
  - Personal information collection with photo upload
  - Professional details and company information
  - Workshop selection and preferences
  - Corporate code validation for team registrations
- **Secure Authentication** (VisitorLogin.php)
  - Email and password authentication with remember-me functionality
  - Password reset and recovery workflow
  - Email verification for new accounts

### Personalized Name Tag System
- **Dynamic Name Tag Generation** (VisitorNameTag.php, PaymentProcess.php)
  - Personalized name tags with visitor photos and professional details
  - Professional photo cropping with Cropper.js integration
  - Precise layout with gold-bordered photos (1115x1115px)
  - Customizable text positioning for name and job title
  - Workshop selection changed from multi-select to single-select dropdown

### Payment Processing
- **Secure Ticket Purchase** (PaymentProcess.php)
  - Stripe payment gateway integration
  - Support for multiple ticket types (free, paid)
  - Secure checkout process with confirmation
  - Payment verification and status tracking
  - Automatic name tag generation upon successful payment

### Social Media Integration
- **LinkedIn Sharing** (ShareOnLinkedin.php)
  - OAuth2 authentication with LinkedIn
  - Automatic name tag sharing on LinkedIn profiles
  - Custom post creation with event hashtags
  - Profile information retrieval and validation

### Event Content Management
- **Dynamic Home Page** (HomePage.php)
  - Featured speakers and agenda items
  - Event sponsors showcase
  - Promotional sliders and content
  - FAQ section for visitor information

### Profile Management
- **Visitor Profile Updates** (VisitorTicket.php, VisitorNameTag.php)
  - Edit personal and professional information
  - Update profile photos with image cropping
  - Country selection with flag display and calling code integration
  - Workshop selection and preferences

### Admin Dashboard
- **Filament Admin Panel** 
  - Modern, responsive admin interface built with Filament
  - Resource management with CRUD operations
  - Role-based access control
  - Real-time data updates

### Admin Dashboard Features
- **Speaker Management** (SpeakerResource.php)
  - Create and manage speaker profiles
  - Upload and manage speaker images
  - Associate speakers with specific tickets
  - Track speaker details including bio, job title, and company
  - Manage speaker social media links (LinkedIn, Facebook)
- **Dashboard Navigation**
  - Organized navigation with grouping (e.g., "Attendees" section)
  - Custom icons for intuitive navigation (e.g., microphone icon for speakers)
  - Responsive layout for desktop and mobile admin access

## Technical Features

- **Laravel Livewire Components** - Real-time, reactive user interfaces without writing JavaScript
- **Alpine.js Integration** - Enhanced frontend interactivity for form handling and UI components
- **Responsive Design** - Mobile-first approach ensuring accessibility across all devices
- **Multi-guard Authentication** - Separate authentication systems for visitors and administrators
- **File Upload & Processing** - Secure image upload with validation and processing
- **International Phone Support** - Country flag display with proper calling code integration
- **Form Validation** - Real-time, client-side validation with server-side verification
- **Stripe Payment Integration** - Secure payment processing with webhook support
- **LinkedIn API Integration** - OAuth2 authentication and content sharing

## User Experience

### Registration Flow
1. Visitor completes multi-step registration form
2. Email verification is sent and validated
3. Visitor selects and purchases event ticket
4. Payment is processed securely through Stripe
5. Name tag is automatically generated with professional details
6. Visitor can share participation on LinkedIn

### Profile Management
1. Visitors can update personal and professional details
2. Photo cropping ensures consistent name tag appearance
3. Workshop preferences can be selected and updated
4. Country selection updates phone display with proper flag and calling code

### Ticket Purchase Process
1. Visitor selects desired ticket type
2. Free tickets are processed immediately
3. Paid tickets redirect to Stripe checkout
4. Upon successful payment, ticket is confirmed
5. Name tag is generated with personalized details
6. Email confirmation is sent with ticket information

## Installation & Configuration

### Prerequisites
- PHP 8.2+
- Composer
- MySQL 8.0+
- Node.js and NPM

### Setup Instructions
1. Clone the repository
2. Install dependencies: `composer install && npm install`
3. Configure environment variables in `.env`
4. Set up Stripe API keys in `.env`:
   ```
   STRIPE_KEY=your_stripe_publishable_key
   STRIPE_SECRET=your_stripe_secret_key
   ```
5. Configure LinkedIn API credentials:
   ```
   LINKEDIN_CLIENT_ID=your_linkedin_client_id
   LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
   LINKEDIN_REDIRECT_URI=your_callback_url
   ```
6. Run migrations: `php artisan migrate`
7. Compile assets: `npm run dev`
8. Start the development server: `php artisan serve`

## Security Features
- CSRF protection on all forms
- Input validation and sanitization
- Secure password handling with hashing
- Rate limiting on authentication attempts
- Signed URLs for email verification

---

 2024 Cairo Beacon Event Management System
