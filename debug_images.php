<?php
require_once 'vendor/autoload.php';

// Boot Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== Comprehensive Image Debug ===\n";

// Test 1: Check all visitor images
$visitors = \App\Models\Visitor::whereNotNull('image')->where('image', '!=', '')->get();
echo "Total visitors with images: " . $visitors->count() . "\n\n";

// Test 2: Check actual files in directory
$visitorImagesDir = public_path('storage/visitor_images');
$files = scandir($visitorImagesDir);
$imageFiles = array_filter($files, function($file) {
    return !in_array($file, ['.', '..']) && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file);
});

echo "Files in visitor_images directory: " . count($imageFiles) . "\n";

// Test 3: Check for case sensitivity issues
$imageFilesLower = array_map('strtolower', $imageFiles);
$issues = [];

foreach($visitors as $visitor) {
    $expectedFile = strtolower($visitor->image);
    if (!in_array($expectedFile, $imageFilesLower)) {
        $issues[] = [
            'visitor_id' => $visitor->id,
            'expected' => $visitor->image,
            'issue' => 'File not found (case sensitive)'
        ];
    }
}

// Test 4: Check for URL accessibility
echo "\nTesting URL accessibility:\n";
$baseUrl = url('storage/visitor_images/');
foreach($visitors as $visitor) {
    $imageUrl = $baseUrl . '/' . $visitor->image;
    $headers = @get_headers($imageUrl);
    $accessible = $headers && strpos($headers[0], '200') !== false;
    echo "  {$visitor->image}: " . ($accessible ? 'Accessible' : 'Not accessible') . "\n";
}

// Test 5: Check TagNameTagGenerator compatibility
use App\Services\TagNameTagGenerator;
$generator = new TagNameTagGenerator();

echo "\nTesting TagNameTagGenerator paths:\n";
foreach($visitors as $visitor) {
    $reflection = new ReflectionClass($generator);
    $method = $reflection->getMethod('getVisitorPhotoPath');
    $method->setAccessible(true);
    $path = $method->invoke($generator, $visitor->image);
    
    echo "  {$visitor->image}:\n";
    echo "    Path: $path\n";
    echo "    Exists: " . (file_exists($path) ? 'YES' : 'NO') . "\n";
}

// Test 6: Check for any special characters in filenames
echo "\nChecking for special characters in filenames:\n";
foreach($visitors as $visitor) {
    if (preg_match('/[^a-zA-Z0-9._-]/', $visitor->image)) {
        echo "  Special chars in: {$visitor->image}\n";
    }
}

// Test 7: Check file permissions
echo "\nChecking file permissions:\n";
foreach($visitors as $visitor) {
    $filePath = public_path('storage/visitor_images/' . $visitor->image);
    if (file_exists($filePath)) {
        $perms = fileperms($filePath);
        echo "  {$visitor->image}: " . substr(sprintf('%o', $perms), -4) . "\n";
    }
}

if (empty($issues)) {
    echo "\n✅ No issues found with visitor images!\n";
} else {
    echo "\n❌ Found issues:\n";
    foreach($issues as $issue) {
        echo "  Visitor {$issue['visitor_id']}: {$issue['expected']} - {$issue['issue']}\n";
    }
}
?>
