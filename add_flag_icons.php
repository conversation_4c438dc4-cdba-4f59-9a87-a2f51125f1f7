<?php

// Path to the country.json file
$jsonFilePath = __DIR__ . '/resources/Data/country.json';

// Read the JSON file
$jsonContent = file_get_contents($jsonFilePath);
$countries = json_decode($jsonContent, true);

// Add flag field to each country for use with flag-icons library
foreach ($countries as &$country) {
    // Get the country code and convert to lowercase for flag-icons CSS class
    $code = strtolower($country['code']);
    
    // Add the flag field to each country
    $country['flag'] = "fi fi-{$code}";
}

// Save the updated JSON back to the file with pretty printing
file_put_contents(
    $jsonFilePath, 
    json_encode($countries, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES)
);

echo "Successfully added flag icons to all " . count($countries) . " countries in the JSON file!\n";
echo "The flag-icons format (fi fi-xx) has been added to each country object.\n";
echo "Remember to include the flag-icons CDN in your layout:\n";
echo "<link rel=\"stylesheet\" href=\"https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css\" />\n";
?>
