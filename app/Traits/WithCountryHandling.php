<?php

namespace App\Traits;

use App\Services\CountryService;

trait WithCountryHandling
{
    public $countries = [];
    public $country = 'EG';
    public $selectedCountryCallingCode = '20';
    public $flagClass = 'fi fi-eg';
    public $whatsappCountryCode = 'EG';
    
    /**
     * Load countries from the service
     */
    public function loadCountries(): void
    {
        $this->countries = CountryService::getAll();
    }
    
    /**
     * Initialize country settings
     */
    public function initializeCountry(): void
    {
        if (empty($this->country)) {
            $this->country = 'EG';
        } else {
            $this->country = strtoupper($this->country);
        }
        
        // Initialize WhatsApp country code if empty
        if (empty($this->whatsappCountryCode)) {
            $this->whatsappCountryCode = $this->country;
        } else {
            $this->whatsappCountryCode = strtoupper($this->whatsappCountryCode);
        }
        
        $this->updateCallingCode();
    }
    
    /**
     * Update the calling code based on selected country
     */
    public function updateCallingCode(): void
    {
        if (empty($this->country)) {
            $this->country = 'EG'; // Default to Egypt
            $this->selectedCountryCallingCode = '20';
            $this->flagClass = 'fi fi-eg'; // Default flag class
            return;
        }

        $countryCode = strtoupper($this->country);

        // Find the country in the loaded countries
        foreach ($this->countries as $country) {
            if (strtoupper($country['code']) === $countryCode) {
                $this->selectedCountryCallingCode = $country['calling_code'] ?? '20';
                // Don't change the country code, just update the calling code and flag
                $this->flagClass = 'fi fi-' . strtolower($country['code']); // Update flag class
                return;
            }
        }

        // Only set defaults if country is not found
        if ($countryCode !== 'EG') {
            $this->selectedCountryCallingCode = '20';
            $this->flagClass = 'fi fi-eg'; // Default flag class
        }
    }
    
    /**
     * This method will be called when the country is updated via wire:model
     */
    public function updatedCountry($value): void
    {
        if (empty($value)) {
            return;
        }

        // Store the country code in uppercase
        $this->country = strtoupper($value);

        // Find the country in the loaded countries
        foreach ($this->countries as $country) {
            if (strtoupper($country['code']) === $this->country) {
                $this->selectedCountryCallingCode = $country['calling_code'] ?? '20';
                $this->flagClass = 'fi fi-' . strtolower($country['code']); // Update flag class

                // Use dispatch for Livewire 3
                $this->dispatch('country-updated', 
                    countryCode: strtolower($country['code']), 
                    callingCode: $this->selectedCountryCallingCode
                );
                break;
            }
        }
    }
    
    /**
     * Get the WhatsApp calling code based on the WhatsApp country code
     */
    public function getWhatsappCallingCode(): string
    {
        if (empty($this->whatsappCountryCode)) {
            return $this->selectedCountryCallingCode;
        }

        $whatsappCountryCode = strtoupper($this->whatsappCountryCode);
        
        // Find the country in the loaded countries
        foreach ($this->countries as $country) {
            if (strtoupper($country['code']) === $whatsappCountryCode) {
                return $country['calling_code'] ?? '20';
            }
        }
        
        return '20'; // Default to Egypt calling code
    }

    /**
     * This method will be called when the WhatsApp country code is updated via wire:model
     */
    public function updatedWhatsappCountryCode($value): void
    {
        if (empty($value)) {
            return;
        }

        // Store the country code in uppercase
        $this->whatsappCountryCode = strtoupper($value);

        // Find the country in the loaded countries
        foreach ($this->countries as $country) {
            if (strtoupper($country['code']) === $this->whatsappCountryCode) {
                $whatsappCallingCode = $country['calling_code'] ?? '20';
                $whatsappFlagClass = 'fi fi-' . strtolower($country['code']); // Update flag class

                // Use dispatch for Livewire 3 with a different event name for WhatsApp
                $this->dispatch('whatsapp-country-updated', 
                    countryCode: strtolower($country['code']), 
                    callingCode: $whatsappCallingCode
                );
                break;
            }
        }
    }
}
