<?php

namespace App\Services;

use App\Models\Agenda;
use App\Models\Visitor;
use App\Models\VisitorTicket;
use App\ValueObjects\VisitorFormData;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

/**
 * Class VisitorRegistrationService
 * @package App\Services
 */
class VisitorRegistrationService
{
    /**
     * Get workshops for a specific day
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getWorkshops()
    {
        return Agenda::orderBy('time')->orderBy('start_from')->whereIn('day',[5,6,7,8])->get();
    }

    /**
     * Get workshops grouped by time (Morning/Evening)
     *
     * @return array
     */
    public function getGroupedWorkshops(): array
    {
        $workshops = $this->getWorkshops();

        return [
            'morning' => $workshops->where('time', 'Morning'),
            'evening' => $workshops->where('time', 'Evening'),
            'other' => $workshops->whereNotIn('time', ['Morning', 'Evening'])
                                ->merge($workshops->whereNull('time'))
        ];
    }

    /**
     * Check if workshop has available spots
     *
     * @param int $workshopId
     * @return bool
     */
    public function isWorkshopAvailable(int $workshopId): bool
    {
        $workshop = Agenda::find($workshopId);
        return $workshop && $workshop->max_attendees > 0;
    }

    /**
     * Get workshop details with availability
     *
     * @param int $workshopId
     * @return array|null
     */
    public function getWorkshopDetails(int $workshopId): ?array
    {
        $workshop = Agenda::find($workshopId);

        if (!$workshop) {
            return null;
        }

        return [
            'id' => $workshop->id,
            'name' => $workshop->name,
            'time' => $workshop->time,
            'start_from' => $workshop->start_from,
            'description' => $workshop->description,
            'max_attendees' => $workshop->max_attendees,
            'available' => $workshop->max_attendees > 0,
            'duration' => $workshop->duration,
        ];
    }

    /**
     * Validate a registration code
     *
     * @param string $code
     * @return bool
     */
    public function validateCode(string $code): bool
    {
        $ticket = VisitorTicket::where('code', $code)->first();
        return true;
    }

    /**
     * Get note for visitor based on code
     *
     * @param string|null $code
     * @return string|null
     */
    public function getVisitorNote(?string $code): ?string
    {
        if (!$code) {
            return null;
        }

        $ticket = VisitorTicket::where(['code' => $code])->first();
        if (!$ticket || $ticket->available_visitor < 1) {
            return null;
        }

        return 'Corporate Ticket, Invited by ' . $ticket->visitor->name . ', ' . $ticket->visitor->company_name;
    }

    /**
     * Register a new visitor without sending verification email
     *
     * @param VisitorFormData $formData
     * @param TemporaryUploadedFile|null $profilePhoto
     * @return Visitor
     */
    public function registerWithoutVerification(VisitorFormData $formData, ?TemporaryUploadedFile $profilePhoto = null): Visitor
    {
        $profilePhotoPath = null;
        if ($profilePhoto) {
            $fullPath = $profilePhoto->store('visitor_images', 'public');
            // Extract only the filename from the full path
            $profilePhotoPath = basename($fullPath);
        }

        $data = $formData->toArray();
        $data['image'] = $profilePhotoPath;
        $data['password'] = Hash::make($data['password']);

        // Get note based on code
        $data['note'] = $this->getVisitorNote($data['corporate_code']);

        // Create the visitor
        $visitor = Visitor::create($data);

        // If visitor has a corporate code, automatically generate name tags
        if ($visitor->corporate_code) {
            try {
                Log::info('Generating name tags for corporate user', ['visitor_id' => $visitor->id, 'corporate_code' => $visitor->corporate_code]);

                // Find the corporate ticket associated with this code
                $corporateTicket = VisitorTicket::where('code', $visitor->corporate_code)->first();

                if ($corporateTicket) {
                    // Create a name tag generator
                    $nameTagGenerator = new TagNameTagGenerator();

                    // Generate both name tag styles
                    $classicNameTagUrl = $nameTagGenerator->generateNameTag($visitor, $corporateTicket);
                    $newNameTagUrl = $nameTagGenerator->generateNewNameTag($visitor, $corporateTicket);

                    // Update the visitor with the name tag URLs
                    $visitor->update([
                        'post_image' => $classicNameTagUrl,
                        'name_tag' => $newNameTagUrl
                    ]);

                    Log::info('Name tags generated successfully for corporate user', [
                        'visitor_id' => $visitor->id,
                        'classic_url' => $classicNameTagUrl,
                        'new_url' => $newNameTagUrl
                    ]);
                } else {
                    Log::warning('Corporate code exists but no matching ticket found', [
                        'visitor_id' => $visitor->id,
                        'corporate_code' => $visitor->corporate_code
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Failed to generate name tags for corporate user', [
                    'visitor_id' => $visitor->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // Don't throw the exception - we still want to create the visitor even if name tag generation fails
            }
        }

        return $visitor;
    }

    /**
     * Send verification email to the visitor
     *
     * @param Visitor $visitor
     * @return void
     */
    public function sendVerificationEmail(Visitor $visitor): void
    {
        event(new Registered($visitor));
    }

    /**
     * Register a new visitor
     *
     *
     * @param VisitorFormData $formData
     * @param TemporaryUploadedFile|null $profilePhoto
     * @return Visitor
     * @deprecated Use registerWithoutVerification() and sendVerificationEmail() separately
     */
    public function register(VisitorFormData $formData, ?TemporaryUploadedFile $profilePhoto = null): Visitor
    {
        $visitor = $this->registerWithoutVerification($formData, $profilePhoto);
        $this->sendVerificationEmail($visitor);
        return $visitor;
    }
}
