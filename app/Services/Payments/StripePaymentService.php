<?php

namespace App\Services\Payments;

use Stripe\Stripe;
use Stripe\Checkout\Session;
use Illuminate\Support\Facades\Log;

class StripePaymentService
{
    /**
     * Initialize Stripe with the API key
     */
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    /**
     * Create a Stripe checkout session
     *
     * @param array $paymentData Payment data including customer email, product name, etc
     * @param float $amount Amount to charge (in AED)
     * @param string $successUrl Success URL for redirect
     * @param string $cancelUrl Cancel URL for redirect
     * @return array Session data including id and redirect URL
     */
    public function createCheckoutSession(array $paymentData, float $amount, string $successUrl, string $cancelUrl): array
    {
        try {
            $session = Session::create([
                'payment_method_types' => ['card'],
                'customer_email' => $paymentData['customer_email'],
                'line_items' => [[
                    'price_data' => [
                        'currency' => 'aed',
                        'unit_amount' => (int)($amount * 3.67 * 100),
                        'product_data' => [
                            'name' => $paymentData['product_name'],
                            'description' => $paymentData['description'] ?? null,
                        ],
                    ],
                    'quantity' => 1,
                ]],
                'mode' => 'payment',
                'success_url' => $successUrl,
                'cancel_url' => $cancelUrl,
            ]);

            return [
                'id' => $session->id,
                'url' => $session->url,
                'success' => true
            ];
        } catch (\Stripe\Exception\ApiErrorException $e) {
            Log::error('Stripe API Error: ' . $e->getMessage(), [
                'error' => $e->getJsonBody()
            ]);
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            Log::error('General Error in Stripe Payment: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify a completed Stripe payment
     *
     * @param string $sessionId The Stripe session ID
     * @return array Payment details including status
     */
    public function verifyPayment(string $sessionId): array
    {
        try {
            $session = Session::retrieve($sessionId);

            return [
                'success' => true,
                'payment_status' => $session->payment_status,
                'payment_intent' => $session->payment_intent ?? null,
                'customer_email' => $session->customer_email ?? null,
                'is_paid' => $session->payment_status === 'paid',
                'session' => $session
            ];
        } catch (\Exception $e) {
            Log::error('Stripe Verification Error: ' . $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
