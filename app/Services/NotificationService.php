<?php

namespace App\Services;

use App\Models\Visitor;
use App\Models\VisitorTicket;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\File;

class NotificationService
{
    /**
     * Send ticket confirmation email to visitor
     *
     * @param Visitor $visitor The visitor who purchased the ticket
     * @param VisitorTicket $ticket The ticket that was purchased
     * @return bool Success status
     */
   public function sendTicketConfirmation(Visitor $visitor, VisitorTicket $ticket): bool
{
    try {
        $qrCodeUrl = null;
        
        // Generate QR code as accessible file
        try {
            $qrCodeUrl = $this->generateQrCodeFile($visitor);
            Log::info('QR code generated successfully', ['url' => $qrCodeUrl]);
        } catch (\Throwable $e) {
            Log::warning('QR file generation failed', [
                'error' => $e->getMessage(),
                'visitor_id' => $visitor->id
            ]);
        }

        Mail::send('mail.ticket', [
            'visitor' => $visitor, 
            'ticket' => $ticket, 
            'qrCodeUrl' => $qrCodeUrl
        ], function ($message) use ($visitor) {
            $message->to($visitor->email)
                ->from(config('mail.from.address'), config('mail.from.name'))
                ->replyTo(config('mail.reply_to.address', config('mail.from.address')), config('mail.reply_to.name', config('mail.from.name')))
                ->subject('Thank you for confirming your attendance');
        });
        
        return true;
    } catch (\Exception $e) {
        Log::error('Failed to send ticket confirmation email: ' . $e->getMessage(), [
            'visitor_id' => $visitor->id,
            'ticket_id' => $ticket->id
        ]);
        
        return false;
    }
}

private function generateQrCodeFile(Visitor $visitor): ?string
{
    try {
        // Create directory in public folder for direct access
        $directory = public_path('qr-codes');
        if (!File::isDirectory($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }
        
        // Generate unique filename
        $filename = 'qr_' . $visitor->id . '_' . hash('md5', $visitor->qr_code) . '.png';
        $filePath = $directory . '/' . $filename;
        
        // Generate QR code if it doesn't exist
        if (!File::exists($filePath)) {
            QrCode::format('png')
                  ->size(160)
                  ->margin(2)
                  ->errorCorrection('H')
                  ->generate($visitor->qr_code, $filePath);
        }
        
        // Return the public URL
        return url('qr-codes/' . $filename);
        
    } catch (\Exception $e) {
        Log::error('QR file generation failed: ' . $e->getMessage());
        return null;
    }
}
}
