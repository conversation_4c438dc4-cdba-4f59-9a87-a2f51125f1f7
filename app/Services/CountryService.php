<?php

namespace App\Services;

class CountryService
{
    /**
     * Get all countries from the JSON file
     *
     * @return array
     */
    public static function getAll(): array
    {
        $jsonPath = resource_path('Data/country.json');
        return json_decode(file_get_contents($jsonPath), true);
    }
    
    /**
     * Get calling code for a specific country code
     *
     * @param string $countryCode
     * @return string
     */
    public static function getCallingCode(string $countryCode): string
    {
        $countries = self::getAll();
        $countryCode = strtoupper($countryCode);
        
        foreach ($countries as $country) {
            if (strtoupper($country['code']) === $countryCode) {
                return $country['calling_code'] ?? '20';
            }
        }
        
        return '20'; // Default to Egypt calling code
    }
    
    /**
     * Get flag class for a specific country code
     *
     * @param string $countryCode
     * @return string
     */
    public static function getFlagClass(string $countryCode): string
    {
        return 'fi fi-' . strtolower($countryCode);
    }

    /**
     * Get country record by dialing prefix digits.
     * Returns null if not found.
     *
     * @param string $callingCodeDigits e.g. "20" or "+20"
     * @return array|null {code, name, calling_code, flag}
     */
    public static function getCountryByCallingCode(string $callingCodeDigits): ?array
    {
        $digits = ltrim($callingCodeDigits, '+');
        foreach (self::getAll() as $country) {
            if ($country['calling_code'] === $digits) {
                return $country;
            }
        }
        return null;
    }
}
