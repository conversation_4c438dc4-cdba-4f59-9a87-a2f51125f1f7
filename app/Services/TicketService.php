<?php

namespace App\Services;

use App\Models\VisitorTicket;
use App\Models\Visitor;
use App\Models\Invoice;
use Random\RandomException;

class TicketService
{
    /**
     * Process a free ticket for a visitor
     *
     * @param Visitor $visitor The visitor
     * @param VisitorTicket $ticket The ticket
     * @return bool Success flag
     */
    public function processFreeTicket(Visitor $visitor, VisitorTicket $ticket): bool
    {
        try {
            $visitor->qr_code = $this->generateUniqueCode();
            $visitor->save();

            $ticket->update(['payment_status' => 1]);

            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing free ticket: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Process a paid ticket after successful payment
     *
     * @param Visitor $visitor The visitor
     * @param VisitorTicket $ticket The ticket
     * @param string $paymentIntentId Payment intent ID from payment gateway
     * @return bool Success flag
     */
    public function processPaidTicket(Visitor $visitor, VisitorTicket $ticket, string $paymentIntentId): bool
    {
        try {
            $visitor->qr_code = $this->generateUniqueCode();
            $visitor->save();

            $ticket->update([
                'payment_id' => $paymentIntentId,
                'payment_status' => 1
            ]);

            // Handle corporate tickets special case
            if ($ticket->ticket->type === 'corporate' || $ticket->ticket->type === 'corporate_bundle') {
                $ticket->update([
                    'available_visitor' => ($ticket->available_visitor - 1),
                    'code' => $this->generateUniqueCode()
                ]);
            }

            // Create invoice record
            Invoice::create([
                'type' => 'inflow',
                'amount' => $ticket->ticket_price,
                'visitor_id' => $visitor->id,
                'paid_for' => $ticket->ticket->name,
                'note' => 'Transaction Id ' . $paymentIntentId,
            ]);

            return true;
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error processing paid ticket: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Generate a unique QR code for visitors
     *
     * @return string Generated code
     * @throws RandomException
     */
    public function generateUniqueCode(): string
    {
        do {
            $code = random_int(10000000000, 99999999999);
        } while (Visitor::where("qr_code", "=", $code)->first());

        return (string)$code;
    }
}
