<?php

namespace App\ValueObjects;

use App\Livewire\VisitorRegister;

class VisitorFormData
{
    public function __construct(
        public string $firstName,
        public string $lastName,
        public string $nationality,
        public string $country,
        public string $email,
        public string $phone,
        public ?string $whatsappNumber,
        public string $password,
        public string $countryCode,
        public ?string $whatsappCountryCode,
        public string $companyName,
        public string $jobTitle,
        public ?string $jobTitle2,
        public ?string $linkedin,
        public string $industry,
        public ?string $code,
        public ?string $discountCode

    ) {}

    /**
     * Create a VisitorFormData instance from a Livewire component
     *
     * @param VisitorRegister $component
     * @return self
     */
    public static function fromLivewire(VisitorRegister $component): self
    {
        return new self(
            $component->firstName,
            $component->lastName,
            $component->nationality,
            $component->country,
            $component->email,
            $component->phone,
            $component->whatsappNumber,
            $component->password,
            $component->countryCode,
            $component->whatsappCountryCode,
            $component->companyName,
            $component->jobTitle,
            $component->jobTitle2,
            $component->linkedin,
            $component->industry,
            $component->code,
            $component->discountCode
        );
    }

    /**
     * Convert the form data to an array for database insertion
     *
     * @return array
     */
    public function toArray(): array
    {
        $jobTitle = $this->jobTitle;
        if (!empty($this->jobTitle2)) {
            $jobTitle .= ' - ' . $this->jobTitle2;
        }

        // Format WhatsApp number with international format if available
        $formattedWhatsappNumber = null;
        if (!empty($this->whatsappNumber) && !empty($this->whatsappCountryCode)) {
            $callingDigits = $this->whatsappCountryCode;
            // If the country code is alphabetical (e.g. EG), convert it to numeric calling code
            if (!ctype_digit($callingDigits)) {
                $callingDigits = \App\Services\CountryService::getCallingCode($callingDigits);
            }
            $formattedWhatsappNumber = '+' . ltrim($callingDigits, '+') . $this->whatsappNumber;
        }

        return [
            'first_name' => $this->firstName,
            'second_name' => $this->lastName,
            'name' => $this->firstName . ' ' . $this->lastName,
            'nationality' => $this->nationality,
            'country_code' => $this->countryCode,
            'country' => $this->country,
            'email' => $this->email,
            'phone' => $this->phone,
            'whatsapp_number' => $formattedWhatsappNumber,
            'password' => $this->password, // Will be hashed in the service
            'company' => $this->companyName,
            'job_title' => $jobTitle,
            'linkedin' => $this->linkedin,
            'company_category' => $this->industry,
            'type' => 'visitor',
            'corporate_code' => $this->code,
            'discount_code' => $this->discountCode,
            'title1' => $this->jobTitle,
            'title2' => $this->jobTitle2,
        ];
    }
}
