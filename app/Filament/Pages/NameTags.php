<?php

namespace App\Filament\Pages;

use App\Models\Visitor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\On;

class NameTags extends Page
{

    protected static ?string $navigationIcon = 'heroicon-o-identification';
    protected static string $view = 'filament.pages.name-tags';
    protected static ?string $navigationGroup = 'Event Management';
    protected static ?string $title = 'Name Tags Management';
    protected static ?string $navigationLabel = 'Name Tags';
    protected static ?int $navigationSort = 3;

    public $searchName = '';
    public $filterReceived = 'all'; // all, received, not_received

    public function getTitle(): string
    {
        return 'Name Tags Management';
    }

    public function getHeading(): string
    {
        return 'Name Tags Management';
    }

    public function getSubheading(): ?string
    {
        return 'Manage name tag distribution for attended visitors';
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) Visitor::whereNotNull('name_tag')
            ->where('attend', true)
            ->where('receive_nameTag', false)
            ->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public function getVisitors()
    {
        return Visitor::query()
            ->whereNotNull('name_tag')
            ->where('attend', true)
            ->when($this->searchName, function (Builder $query) {
                $query->where(function ($q) {
                    $q->where('first_name', 'like', "%{$this->searchName}%")
                      ->orWhere('second_name', 'like', "%{$this->searchName}%")
                      ->orWhere('family_name', 'like', "%{$this->searchName}%")
                      ->orWhere('name', 'like', "%{$this->searchName}%");
                });
            })
            ->when($this->filterReceived === 'received', function (Builder $query) {
                $query->where('receive_nameTag', true);
            })
            ->when($this->filterReceived === 'not_received', function (Builder $query) {
                $query->where('receive_nameTag', false);
            })
            ->orderBy('receive_nameTag', 'asc')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    public function markNameTagReceived($visitorId): void
    {
        $visitor = Visitor::find($visitorId);

        if (!$visitor) {
            return;
        }

        $visitor->update([
            'receive_nameTag' => true,
        ]);

        Notification::make()
            ->title('✅ Name Tag Received!')
            ->body("Marked {$visitor->first_name} {$visitor->second_name} as having received their name tag.")
            ->success()
            ->duration(5000)
            ->send();

        $this->dispatch('$refresh');
    }

    public function unmarkNameTagReceived($visitorId): void
    {
        $visitor = Visitor::find($visitorId);

        if (!$visitor) {
            return;
        }

        $visitor->update([
            'receive_nameTag' => false,
        ]);

        Notification::make()
            ->title('Name Tag Unmarked')
            ->body("Unmarked {$visitor->first_name} {$visitor->second_name} name tag as not received.")
            ->warning()
            ->duration(5000)
            ->send();

        $this->dispatch('$refresh');
    }

    public function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refreshStats')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(fn () => $this->dispatch('$refresh')),
        ];
    }
}
