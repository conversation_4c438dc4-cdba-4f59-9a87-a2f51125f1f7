<?php

namespace App\Filament\Pages;

use App\Models\Visitor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Concerns\InteractsWithTable;
use Filament\Tables\Contracts\HasTable;
use Illuminate\Database\Eloquent\Builder;
use Livewire\Attributes\On;

class NameTags extends Page implements HasTable
{
    use InteractsWithTable;

    protected static ?string $navigationIcon = 'heroicon-o-identification';
    protected static string $view = 'filament.pages.name-tags';
    protected static ?string $navigationGroup = 'Event Management';
    protected static ?string $title = 'Name Tags Management';
    protected static ?string $navigationLabel = 'Name Tags';
    protected static ?int $navigationSort = 3;

    public $searchName = '';
    public $searchEmail = '';
    public $searchCompany = '';
    public $filterReceived = 'all'; // all, received, not_received

    public function getTitle(): string
    {
        return 'Name Tags Management';
    }

    public function getHeading(): string
    {
        return 'Name Tags Management';
    }

    public function getSubheading(): ?string
    {
        return 'Manage name tag distribution for attended visitors';
    }

    public static function getNavigationBadge(): ?string
    {
        return (string) Visitor::whereNotNull('name_tag')
            ->where('attend', true)
            ->where('receive_nameTag', false)
            ->count();
    }

    public static function getNavigationBadgeColor(): ?string
    {
        return 'warning';
    }

    public function table(Table $table): Table
    {
        return $table
            ->query(
                Visitor::query()
                    ->whereNotNull('name_tag')
                    ->where('attend', true)
                    ->when($this->searchName, function (Builder $query) {
                        $query->where(function ($q) {
                            $q->where('first_name', 'like', "%{$this->searchName}%")
                              ->orWhere('second_name', 'like', "%{$this->searchName}%")
                              ->orWhere('family_name', 'like', "%{$this->searchName}%")
                              ->orWhere('name', 'like', "%{$this->searchName}%");
                        });
                    })
                    ->when($this->searchEmail, function (Builder $query) {
                        $query->where('email', 'like', "%{$this->searchEmail}%");
                    })
                    ->when($this->searchCompany, function (Builder $query) {
                        $query->where('company', 'like', "%{$this->searchCompany}%");
                    })
                    ->when($this->filterReceived === 'received', function (Builder $query) {
                        $query->where('receive_nameTag', true);
                    })
                    ->when($this->filterReceived === 'not_received', function (Builder $query) {
                        $query->where('receive_nameTag', false);
                    })
                    ->orderBy('receive_nameTag', 'asc')
                    ->orderBy('created_at', 'desc')
            )
            ->columns([
                Tables\Columns\TextColumn::make('image')
                    ->label('Photo')
                    ->formatStateUsing(function ($state, Visitor $record) {
                        if (!$state) {
                            return '<img src="' . asset('images/placeholder-nametag.png') . '" class="rounded-lg w-12 h-12 object-cover" alt="No Photo" />';
                        }

                        $imageUrl = $record->formatted_image ?? Visitor::formatImagePath($state, 'storage/visitor_images');
                        return '<img src="' . $imageUrl . '" class="rounded-lg w-12 h-12 object-cover" alt="Visitor Photo" />';
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('full_name')
                    ->label('Name')
                    ->getStateUsing(fn (Visitor $record) => trim($record->first_name . ' ' . $record->second_name))
                    ->searchable(['first_name', 'second_name'])
                    ->sortable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('company')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('title1')
                    ->label('Job Title')
                    ->searchable()
                    ->toggleable(),
                Tables\Columns\IconColumn::make('receive_nameTag')
                    ->label('Received')
                    ->boolean()
                    ->sortable()
                    ->action(function (Visitor $record) {
                        if ($record->receive_nameTag) {
                            $this->unmarkNameTagReceived($record);
                        } else {
                            $this->markNameTagReceived($record);
                        }
                    }),
                Tables\Columns\TextColumn::make('name_tag')
                    ->label('Name Tag')
                    ->formatStateUsing(function ($state) {
                        if (!$state) {
                            return 'No name tag';
                        }
                        return '<span class="text-green-600 font-medium">Available</span>';
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('attended_at')
                    ->label('Attended At')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('receive_nameTag')
                    ->label('Received Status')
                    ->options([
                        '' => 'All',
                        '1' => 'Received',
                        '0' => 'Not Received',
                    ])
                    ->default(''),
            ])
            ->actions([
                Tables\Actions\Action::make('markReceived')
                    ->label('Mark Received')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->visible(fn (Visitor $record) => !$record->receive_nameTag)
                    ->action(fn (Visitor $record) => $this->markNameTagReceived($record)),

                Tables\Actions\Action::make('unmarkReceived')
                    ->label('Unmark Received')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->visible(fn (Visitor $record) => $record->receive_nameTag)
                    ->requiresConfirmation()
                    ->action(fn (Visitor $record) => $this->unmarkNameTagReceived($record)),

                Tables\Actions\Action::make('viewNameTag')
                    ->label('View Name Tag')
                    ->icon('heroicon-o-eye')
                    ->color('primary')
                    ->url(function (Visitor $record) {
                        if ($record->name_tag) {
                            return Visitor::formatImagePath($record->name_tag, 'images/name_tags');
                        }
                        return null;
                    })
                    ->openUrlInNewTab()
                    ->visible(fn (Visitor $record) => !empty($record->name_tag)),

                Tables\Actions\Action::make('downloadNameTag')
                    ->label('Download')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(function (Visitor $record) {
                        if ($record->name_tag) {
                            return Visitor::formatImagePath($record->name_tag, 'images/name_tags');
                        }
                        return null;
                    })
                    ->openUrlInNewTab()
                    ->visible(fn (Visitor $record) => !empty($record->name_tag)),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('markAllReceived')
                    ->label('Mark as Received')
                    ->icon('heroicon-o-check-circle')
                    ->color('success')
                    ->action(function ($records) {
                        $count = 0;
                        foreach ($records as $record) {
                            if (!$record->receive_nameTag) {
                                $record->update(['receive_nameTag' => true]);
                                $count++;
                            }
                        }

                        Notification::make()
                            ->title('Bulk Update Successful')
                            ->body("Marked {$count} visitors as having received their name tags.")
                            ->success()
                            ->send();

                        $this->resetTable();
                    })
                    ->requiresConfirmation(),

                Tables\Actions\BulkAction::make('unmarkAllReceived')
                    ->label('Unmark as Received')
                    ->icon('heroicon-o-x-circle')
                    ->color('warning')
                    ->action(function ($records) {
                        $count = 0;
                        foreach ($records as $record) {
                            if ($record->receive_nameTag) {
                                $record->update(['receive_nameTag' => false]);
                                $count++;
                            }
                        }

                        Notification::make()
                            ->title('Bulk Update Successful')
                            ->body("Unmarked {$count} visitors name tags as not received.")
                            ->warning()
                            ->send();

                        $this->resetTable();
                    })
                    ->requiresConfirmation(),
            ])
            ->defaultSort('receive_nameTag', 'asc')
            ->striped()
            ->paginated([10, 25, 50, 100]);
    }

    public function markNameTagReceived(Visitor $visitor): void
    {
        $visitor->update([
            'receive_nameTag' => true,
        ]);

        Notification::make()
            ->title('✅ Name Tag Received!')
            ->body("Marked {$visitor->first_name} {$visitor->second_name} as having received their name tag.")
            ->success()
            ->duration(5000)
            ->send();

        $this->resetTable();
    }

    public function unmarkNameTagReceived(Visitor $visitor): void
    {
        $visitor->update([
            'receive_nameTag' => false,
        ]);

        Notification::make()
            ->title('Name Tag Unmarked')
            ->body("Unmarked {$visitor->first_name} {$visitor->second_name} name tag as not received.")
            ->warning()
            ->duration(5000)
            ->send();

        $this->resetTable();
    }

    public function getHeaderActions(): array
    {
        return [
            \Filament\Actions\Action::make('refreshStats')
                ->label('Refresh')
                ->icon('heroicon-o-arrow-path')
                ->action(fn () => $this->resetTable()),
        ];
    }
}
