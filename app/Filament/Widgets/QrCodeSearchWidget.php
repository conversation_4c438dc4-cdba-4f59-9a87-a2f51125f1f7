<?php

namespace App\Filament\Widgets;

use App\Models\Visitor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Widgets\Widget;
use Livewire\Attributes\On;

class QrCodeSearchWidget extends Widget
{
    protected static string $view = 'filament.widgets.qr-code-search';
    
    protected int | string | array $columnSpan = 'full';
    
    public $qrCode = '';
    public $showVisitorModal = false;
    public $selectedVisitor = null;

    public function updatedQrCode()
    {
        if (!empty($this->qrCode) && strlen($this->qrCode) > 3) {
            $this->searchVisitorByQrCode();
        }
    }

    public function searchVisitorByQrCode(): void
    {
        if (empty($this->qrCode)) {
            Notification::make()
                ->title('Error')
                ->body('Please enter a QR code.')
                ->danger()
                ->send();
            return;
        }

        $visitor = Visitor::where('qr_code', $this->qrCode)->first();

        if (!$visitor) {
            Notification::make()
                ->title('QR Code Not Found')
                ->body('No visitor found with the provided QR code.')
                ->warning()
                ->send();
            $this->reset(['qrCode']);
            return;
        }

        // Set the selected visitor and show modal
        $this->selectedVisitor = $visitor;
        $this->showVisitorModal = true;
    }

    public function markAttendance(): void
    {
        if (!$this->selectedVisitor) {
            return;
        }

        if ($this->selectedVisitor->attend) {
            Notification::make()
                ->title('Already Attended')
                ->body("Visitor {$this->selectedVisitor->first_name} {$this->selectedVisitor->second_name} has already been marked as attended.")
                ->warning()
                ->duration(5000)
                ->send();
            $this->closeModal();
            return;
        }

        $this->selectedVisitor->update([
            'attend' => true,
            'attended_at' => now(),
        ]);

        Notification::make()
            ->title('✅ Attendance Marked Successfully!')
            ->body("ID: {$this->selectedVisitor->id} - {$this->selectedVisitor->first_name} {$this->selectedVisitor->second_name}\n" .
                   "Email: {$this->selectedVisitor->email}\n" .
                   "Company: {$this->selectedVisitor->company}\n" .
                   "Attended at: " . now()->format('Y-m-d H:i:s'))
            ->success()
            ->duration(8000)
            ->send();

        $this->closeModal();
        $this->dispatch('refreshTable');
    }

    public function unmarkAttendance(): void
    {
        if (!$this->selectedVisitor) {
            return;
        }

        if (!$this->selectedVisitor->attend) {
            Notification::make()
                ->title('Not Attended')
                ->body("Visitor {$this->selectedVisitor->first_name} {$this->selectedVisitor->second_name} is not marked as attended.")
                ->warning()
                ->send();
            return;
        }

        $this->selectedVisitor->update([
            'attend' => false,
            'attended_at' => null,
        ]);

        Notification::make()
            ->title('Attendance Unmarked')
            ->body("Successfully unmarked {$this->selectedVisitor->first_name} {$this->selectedVisitor->second_name} attendance.")
            ->success()
            ->send();

        $this->closeModal();
        $this->dispatch('refreshTable');
    }

    public function closeModal(): void
    {
        $this->showVisitorModal = false;
        $this->selectedVisitor = null;
        $this->reset(['qrCode']);
    }

    #[On('refreshTable')]
    public function refreshTable(): void
    {
        // This will be caught by the parent page to refresh the table
        $this->dispatch('$refresh');
    }
}
