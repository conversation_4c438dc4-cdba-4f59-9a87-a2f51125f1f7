<?php

namespace App\Filament\Resources\VisitorResource\Pages;

use App\Filament\Resources\VisitorResource;
use App\Models\Visitor;
use App\Models\Agenda;
use Filament\Actions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Components\Tab;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class ListVisitors extends ListRecords
{
    protected static string $resource = VisitorResource::class;

    public $qrCode = '';
    public $searchName = '';
    public $searchEmail = '';
    public $searchCompany = '';
    public $searchPhone = '';
    public $selectedWorkshop = null;
    public $showVisitorModal = false;
    public $selectedVisitor = null;

    protected function getHeaderActions(): array
    {
        $actions = [];

        // Workshop Filter Action (only show on workshops tab)
        if ($this->activeTab === 'workshops') {
            $actions[] = Actions\Action::make('workshopFilter')
                ->label('Filter by Workshop')
                ->icon('heroicon-o-funnel')
                ->color('primary')
                ->form([
                    Forms\Components\Select::make('workshop')
                        ->label('Select Workshop')
                        ->placeholder('All Workshops')
                        ->options(function () {
                            $workshops = Agenda::orderBy('time')->orderBy('start_from')->whereIn('day',[5,6,7,8])->get();
                            $options = ['all' => 'All Workshops'];

                            $morningWorkshops = $workshops->where('time', 'Morning');
                            $eveningWorkshops = $workshops->where('time', 'Evening');

                            if ($morningWorkshops->count() > 0) {
                                foreach ($morningWorkshops as $workshop) {
                                    $options[$workshop->id] = "Morning: {$workshop->name}";
                                }
                            }

                            if ($eveningWorkshops->count() > 0) {
                                foreach ($eveningWorkshops as $workshop) {
                                    $options[$workshop->id] = "Evening: {$workshop->name}";
                                }
                            }

                            // Add workshops without time specified
                            $otherWorkshops = $workshops->whereNull('time')->where('time', '!=', 'Morning')->where('time', '!=', 'Evening');
                            foreach ($otherWorkshops as $workshop) {
                                $options[$workshop->id] = $workshop->name;
                            }

                            return $options;
                        })
                        ->default($this->selectedWorkshop ?: 'all'),
                ])
                ->action(function (array $data) {
                    $this->selectedWorkshop = $data['workshop'] === 'all' ? null : $data['workshop'];
                    $this->resetTable();

                    $workshopName = $this->selectedWorkshop
                        ? Agenda::find($this->selectedWorkshop)?->name
                        : 'All Workshops';

                    Notification::make()
                        ->title('Workshop Filter Applied')
                        ->body("Now showing visitors for: {$workshopName}")
                        ->success()
                        ->send();
                });
        }

        return $actions;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            \App\Filament\Widgets\QrCodeSearchWidget::class,
        ];
    }

    public function getTabs(): array
    {
        return [
            'all' => Tab::make('All Visitors'),
            'with_qr' => Tab::make('With QR Codes')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('qr_code'))
                ->badge(Visitor::whereNotNull('qr_code')->count()),
            'attended' => Tab::make('Attended')
                ->modifyQueryUsing(fn (Builder $query) => $query->where('attend', true))
                ->badge(Visitor::where('attend', true)->count()),
            'name_tags' => Tab::make('Name Tags')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotNull('name_tag')->where('attend', true))
                ->badge(Visitor::whereNotNull('name_tag')->where('attend', true)->count()),
            'workshops' => Tab::make('Workshops')
                ->modifyQueryUsing(fn (Builder $query) => $this->applyWorkshopFilter($query))
                ->badge($this->getWorkshopVisitorsCount()),
        ];
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'all';
    }

    public function updatedQrCode()
    {
        if (!empty($this->qrCode) && strlen($this->qrCode) > 5) {
            $this->markAttendanceByQrCode();
        }
    }

    public function updatedActiveTab(): void
    {
        parent::updatedActiveTab();
        $this->resetTable();
    }

    public function markAttendanceByQrCode(): void
    {
        if (empty($this->qrCode)) {
            Notification::make()
                ->title('Error')
                ->body('Please enter a QR code.')
                ->danger()
                ->send();
            return;
        }

        $visitor = Visitor::where('qr_code', $this->qrCode)->first();

        if (!$visitor) {
            Notification::make()
                ->title('QR Code Not Found')
                ->body('No visitor found with the provided QR code.')
                ->warning()
                ->send();
            return;
        }

        if ($visitor->attend) {
            Notification::make()
                ->title('Already Attended')
                ->body("Visitor ID: {$visitor->id} - {$visitor->first_name} {$visitor->second_name} has already been marked as attended.")
                ->warning()
                ->duration(5000)
                ->send();
            return;
        }

        $visitor->update([
            'attend' => true,
            'attended_at' => now(),
        ]);

        Notification::make()
            ->title('✅ Attendance Marked Successfully!')
            ->body("ID: {$visitor->id} - {$visitor->first_name} {$visitor->second_name}\n" .
                   "Email: {$visitor->email}\n" .
                   "Company: {$visitor->company}\n" .
                   "Attended at: " . now()->format('Y-m-d H:i:s'))
            ->success()
            ->duration(8000)
            ->send();

        $this->qrCode = '';

        $this->resetTable();
    }

    public function markNameTagReceived(Visitor $visitor): void
    {
        $visitor->update([
            'receive_nameTag' => true,
        ]);

        Notification::make()
            ->title('✅ Name Tag Received!')
            ->body("Marked {$visitor->first_name} {$visitor->second_name} as having received their name tag.")
            ->success()
            ->duration(5000)
            ->send();

        $this->resetTable();
        $this->dispatch('$refresh');
    }

    public function unmarkNameTagReceived(Visitor $visitor): void
    {
        $visitor->update([
            'receive_nameTag' => false,
        ]);

        Notification::make()
            ->title('Name Tag Unmarked')
            ->body("Unmarked {$visitor->first_name} {$visitor->second_name} name tag as not received.")
            ->warning()
            ->duration(5000)
            ->send();


        $this->resetTable();
        $this->dispatch('$refresh');
    }

    public function performSearch(): void
    {
        $this->resetTable();
    }

    public function clearSearch(): void
    {
        $this->searchName = '';
        $this->searchEmail = '';
        $this->searchCompany = '';
        $this->searchPhone = '';
        $this->resetTable();
    }

    protected function applySearchFilters(Builder $query): Builder
    {
        if (!empty($this->searchName)) {
            $query->where(function ($q) {
                $q->where('first_name', 'like', "%{$this->searchName}%")
                  ->orWhere('second_name', 'like', "%{$this->searchName}%")
                  ->orWhere('family_name', 'like', "%{$this->searchName}%")
                  ->orWhere('name', 'like', "%{$this->searchName}%");
            });
        }

        if (!empty($this->searchEmail)) {
            $query->where('email', 'like', "%{$this->searchEmail}%");
        }

        if (!empty($this->searchCompany)) {
            $query->where('company', 'like', "%{$this->searchCompany}%");
        }

        if (!empty($this->searchPhone)) {
            $query->where(function ($q) {
                $q->where('phone', 'like', "%{$this->searchPhone}%")
                  ->orWhere('whatsapp_number', 'like', "%{$this->searchPhone}%");
            });
        }

        return $query;
    }

    protected function applyWorkshopFilter(Builder $query): Builder
    {
        // Base filter: attended visitors with name tags
        $query->where('attend', true)
              ->whereNotNull('name_tag')
              ->where('receive_nameTag', true);

        // If a specific workshop is selected, filter by that workshop
        if ($this->selectedWorkshop) {
            $query->whereHas('agendas', function ($q) {
                $q->where('agenda_id', $this->selectedWorkshop);
            });
        } else {
            // If no workshop selected, show visitors who have any workshop
            $query->whereHas('agendas');
        }

        return $query;
    }

    protected function getWorkshopVisitorsCount(): int
    {
        if ($this->selectedWorkshop) {
            return Visitor::where('attend', true)
                          ->whereNotNull('name_tag')
                          ->where('receive_nameTag', true)
                          ->whereHas('agendas', function ($q) {
                              $q->where('agenda_id', $this->selectedWorkshop);
                          })
                          ->count();
        }

        return Visitor::where('attend', true)
                      ->whereNotNull('name_tag')
                      ->where('receive_nameTag', true)
                      ->whereHas('agendas')
                      ->count();
    }

    public function updatedSelectedWorkshop()
    {
        $this->resetTable();
    }

    public function table(Table $table): Table
    {
        $table = parent::table($table);

        // Add specific actions based on the active tab
        if (in_array($this->activeTab, ['with_qr', 'attended', 'name_tags', 'workshops'])) {
            $actions = [];

            // Attendance actions for QR and attended tabs
            $actions[] = Tables\Actions\Action::make('markAttended')
                ->label('Mark Attended')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn (Visitor $record) =>
                    in_array($this->activeTab, ['with_qr', 'attended']) && !$record->attend
                )
                ->action(function (Visitor $record) {
                    $record->update([
                        'attend' => true,
                        'attended_at' => now(),
                    ]);

                    Notification::make()
                        ->title('Attendance Marked')
                        ->body("Successfully marked {$record->first_name} {$record->second_name} as attended.")
                        ->success()
                        ->send();

                    $this->resetTable();
                });

            $actions[] = Tables\Actions\Action::make('unmarkAttended')
                ->label('Unmark Attended')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn (Visitor $record) =>
                    in_array($this->activeTab, ['with_qr', 'attended']) && $record->attend
                )
                ->requiresConfirmation()
                ->action(function (Visitor $record) {
                    $record->update([
                        'attend' => false,
                        'attended_at' => null,
                    ]);

                    Notification::make()
                        ->title('Attendance Unmarked')
                        ->body("Successfully unmarked {$record->first_name} {$record->second_name} attendance.")
                        ->success()
                        ->send();

                    $this->resetTable();
                });

            // Name tag actions for name tags tab
            $actions[] = Tables\Actions\Action::make('markNameTagReceived')
                ->label('Mark Received')
                ->icon('heroicon-o-identification')
                ->color('success')
                ->visible(fn (Visitor $record) =>
                    $this->activeTab === 'name_tags' && !$record->receive_nameTag
                )
                ->action(fn (Visitor $record) => $this->markNameTagReceived($record));

            $actions[] = Tables\Actions\Action::make('unmarkNameTagReceived')
                ->label('Unmark Received')
                ->icon('heroicon-o-x-mark')
                ->color('warning')
                ->visible(fn (Visitor $record) =>
                    $this->activeTab === 'name_tags' && $record->receive_nameTag
                )
                ->requiresConfirmation()
                ->action(fn (Visitor $record) => $this->unmarkNameTagReceived($record));

            $actions[] = Tables\Actions\Action::make('viewNameTag')
                ->label('View Name Tag')
                ->icon('heroicon-o-eye')
                ->color('primary')
                ->url(function (Visitor $record) {
                    if ($record->name_tag) {
                        return Visitor::formatImagePath($record->name_tag, 'images/name_tags');
                    }
                    return null;
                })
                ->openUrlInNewTab()
                ->visible(fn (Visitor $record) =>
                    $this->activeTab === 'name_tags' && !empty($record->name_tag)
                );

            // Workshop-specific actions
            $actions[] = Tables\Actions\Action::make('viewWorkshops')
                ->label('View Workshops')
                ->icon('heroicon-o-academic-cap')
                ->color('info')
                ->visible(fn (Visitor $record) => $this->activeTab === 'workshops')
                ->action(function (Visitor $record) {
                    $workshops = $record->agendas;
                    $workshopNames = $workshops->pluck('name')->join(', ');

                    Notification::make()
                        ->title('Visitor Workshops')
                        ->body("Workshops for {$record->first_name} {$record->second_name}: " . ($workshopNames ?: 'No workshops assigned'))
                        ->info()
                        ->duration(8000)
                        ->send();
                });

            $actions[] = Tables\Actions\ViewAction::make();
            $actions[] = Tables\Actions\EditAction::make();

            $table->actions($actions);

            $bulkActions = [];

            $bulkActions[] = Tables\Actions\BulkAction::make('markAttended')
                ->label('Mark as Attended')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn () => in_array($this->activeTab, ['with_qr', 'attended']))
                ->action(function ($records) {
                    $count = 0;
                    foreach ($records as $record) {
                        if (!$record->attend) {
                            $record->update([
                                'attend' => true,
                                'attended_at' => now(),
                            ]);
                            $count++;
                        }
                    }

                    Notification::make()
                        ->title('Bulk Attendance Marked')
                        ->body("Successfully marked {$count} visitors as attended.")
                        ->success()
                        ->send();

                    $this->resetTable();
                })
                ->requiresConfirmation();

            $bulkActions[] = Tables\Actions\BulkAction::make('unmarkAttended')
                ->label('Unmark Attendance')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn () => in_array($this->activeTab, ['with_qr', 'attended']))
                ->action(function ($records) {
                    $count = 0;
                    foreach ($records as $record) {
                        if ($record->attend) {
                            $record->update([
                                'attend' => false,
                                'attended_at' => null,
                            ]);
                            $count++;
                        }
                    }

                    Notification::make()
                        ->title('Bulk Attendance Unmarked')
                        ->body("Successfully unmarked {$count} visitors attendance.")
                        ->success()
                        ->send();

                    $this->resetTable();
                })
                ->requiresConfirmation();

            // Name tag bulk actions for name tags tab
            $bulkActions[] = Tables\Actions\BulkAction::make('markNameTagsReceived')
                ->label('Mark Name Tags Received')
                ->icon('heroicon-o-identification')
                ->color('success')
                ->visible(fn () => $this->activeTab === 'name_tags')
                ->action(function ($records) {
                    $count = 0;
                    foreach ($records as $record) {
                        if (!$record->receive_nameTag) {
                            $record->update(['receive_nameTag' => true]);
                            $count++;
                        }
                    }

                    Notification::make()
                        ->title('Bulk Name Tags Marked')
                        ->body("Successfully marked {$count} visitors as having received their name tags.")
                        ->success()
                        ->send();

                    $this->resetTable();
                })
                ->requiresConfirmation();

            $bulkActions[] = Tables\Actions\BulkAction::make('unmarkNameTagsReceived')
                ->label('Unmark Name Tags Received')
                ->icon('heroicon-o-x-mark')
                ->color('warning')
                ->visible(fn () => $this->activeTab === 'name_tags')
                ->action(function ($records) {
                    $count = 0;
                    foreach ($records as $record) {
                        if ($record->receive_nameTag) {
                            $record->update(['receive_nameTag' => false]);
                            $count++;
                        }
                    }

                    Notification::make()
                        ->title('Bulk Name Tags Unmarked')
                        ->body("Successfully unmarked {$count} visitors name tags as not received.")
                        ->warning()
                        ->send();

                    $this->resetTable();
                })
                ->requiresConfirmation();

            if (!empty($bulkActions)) {
                $table->bulkActions([
                    Tables\Actions\BulkActionGroup::make($bulkActions)
                ]);
            }
        }

        return $table;
    }
}
