<?php

namespace App\Filament\Resources\SpeakerResource\Pages;

use App\Filament\Resources\SpeakerResource;
use App\Models\Ticket;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreateSpeaker extends CreateRecord
{
    protected static string $resource = SpeakerResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Ensure type is set to speaker
        $data['type'] = 'speaker';

        // Associate with authenticated user
        $data['user_id'] = auth()->id();

        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        // Create the speaker
        $speaker = $this->getModel()::create($data);

        // Attach the ticket if provided
        if (!empty($data['ticket_id'])) {
            $ticket = Ticket::find($data['ticket_id']);
            if ($ticket) {
                $speaker->tickets()->attach($ticket->id, [
                    'ticket_price' => $ticket->discount ?: $ticket->price,
                    'note' => $data['bio'] ?? null,
                    'approved' => 1,
                    'user_id' => auth()->id(),
                    'available_visitor' => $ticket->attendees_number ?? null,
                    'event_id' => $ticket->event_id,
                ]);
            }
        }

        return $speaker;
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('index');
    }
}
