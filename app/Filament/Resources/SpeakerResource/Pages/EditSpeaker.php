<?php

namespace App\Filament\Resources\SpeakerResource\Pages;

use App\Filament\Resources\SpeakerResource;
use App\Models\Ticket;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;

class EditSpeaker extends EditRecord
{
    protected static string $resource = SpeakerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        // Because a speaker may have multiple tickets, pick the
        // currently "approved" speaker ticket or whichever fits your logic.
        $attachedTicket = $this->record
            ->tickets()
            ->wherePivot('approved', 1)
            ->first();

        // Make sure 'ticket_id' is set to the attached ticket's ID,
        // if one exists. This ensures Filament "pre-selects" that option.
        $data['ticket_id'] = $attachedTicket?->id;

        // Keep this visitor's type set to 'speaker' even when editing.
        $data['type'] = 'speaker';

        return $data;
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        // Update speaker data
        $record->update($data);

        // Handle ticket attachment if ticket_id is provided
        if (!empty($data['ticket_id'])) {
            $ticket = Ticket::find($data['ticket_id']);

            if ($ticket) {
                // First detach any existing speaker tickets to avoid duplicates
                $record->tickets()->wherePivot('approved', 1)->detach();

                // Then attach the new ticket
                $record->tickets()->attach($ticket->id, [
                    'ticket_price' => $ticket->discount ?: $ticket->price,
                    'note' => $data['bio'] ?? null,
                    'approved' => 1,
                    'user_id' => auth()->id(),
                    'available_visitor' => $ticket->attendees_number ?? null,
                    'event_id' => $ticket->event_id,
                ]);
            }
        }

        return $record;
    }

    protected function getRedirectUrl(): string
    {
        return self::getResource()::getUrl('index');
    }
}
