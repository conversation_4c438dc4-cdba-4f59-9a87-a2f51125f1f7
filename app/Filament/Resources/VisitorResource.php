<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VisitorResource\Pages;
use App\Models\Visitor;
use App\Services\TagNameTagGenerator;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Storage;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Actions\Action;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class VisitorResource extends Resource
{
    protected static ?string $model = Visitor::class;
    protected static ?string $navigationIcon = 'heroicon-o-user-group';
    protected static ?string $navigationGroup = 'Event Management';
    protected static ?string $recordTitleAttribute = 'full_name';

    public static function getNavigationLabel(): string
    {
        return __('Visitors');
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function form(Form $form): Form
    {

        return $form
            ->schema([
                Forms\Components\Section::make('Personal Information')
                    ->schema([
                        Forms\Components\TextInput::make('first_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('second_name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('title1')
                            ->label('Job Title')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('title2')
                            ->label('Secondary Job Title')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('company')
                            ->label('Company')
                            ->maxLength(255),
                        Forms\Components\FileUpload::make('image')
                            ->label('Profile Photo')
                            ->image()
                            ->directory('visitor_images')
                            ->disk('public')
                            ->visibility('public')
                            ->imageResizeMode('contain')
                            ->imagePreviewHeight('250')
                            ->maxSize(1024)
                            ->getUploadedFileNameForStorageUsing(
                                fn(TemporaryUploadedFile $file): string => 'visitor_' . time() . '_' . $file->getClientOriginalName()
                            )
                            ->dehydrateStateUsing(function ($state) {
                                if (is_string($state) && str_contains($state, '/')) {
                                    return basename($state);
                                }
                                return $state;
                            })
                            ->afterStateHydrated(function (Forms\Components\FileUpload $component, $state) {
                                if (empty($state)) {
                                    return;
                                }

                                // If it's just a filename (no path separator), prepend the directory
                                if (!str_contains($state, '/')) {
                                    $component->state(['visitor_images/' . $state]);
                                }
                            }),
                    ])->columns(2),

                Forms\Components\Section::make('Contact Information')
                    ->schema([
                        Forms\Components\TextInput::make('phone')
                            ->tel()
                            ->maxLength(20),
                        Forms\Components\TextInput::make('whatsapp_number')
                            ->label('WhatsApp Number')
                            ->maxLength(20),
                        Forms\Components\TextInput::make('linkedin')
                            ->label('LinkedIn Profile')
                            ->url()
                            ->maxLength(255),
                        Forms\Components\Select::make('country_code')
                            ->label('Country')
                            ->options(function () {
                                $countries = json_decode(file_get_contents(resource_path('Data/country.json')), true);
                                $countryOptions = [];
                                foreach ($countries as $country) {
                                    $countryOptions[$country['code']] = $country['name'];
                                }
                                return $countryOptions;
                            }),
                        Forms\Components\Select::make('whatsapp_country_code')
                            ->label('WhatsApp Country Code')
                            ->options(function () {
                                $countries = json_decode(file_get_contents(resource_path('Data/country.json')), true);
                                $countryOptions = [];
                                foreach ($countries as $country) {
                                    $countryOptions[$country['code']] = "({$country['calling_code']}) {$country['name']}";
                                }
                                return $countryOptions;
                            }),
                    ])->columns(2),

                Forms\Components\Section::make('Event Information')
                    ->schema([
                        Forms\Components\Select::make('agendas')
                            ->relationship('agendas', 'name')
                            ->multiple()
                            ->preload()
                            ->label('Selected Workshops'),
                        Forms\Components\Textarea::make('note')
                            ->maxLength(65535)
                            ->columnSpanFull(),
                    ]),

                Forms\Components\Section::make('Name Tags')
                    ->schema([
                        Forms\Components\Placeholder::make('name_tag_preview')
                            ->label('Social Media Name Tag')
                            ->content(function (Visitor $record) {
                                if ($record->post_image) {
                                    // Try local path first, then fallback to external URL
                                    $localPath = public_path('images/new_name_tags/' . $record->post_image);
                                    $url = file_exists($localPath)
                                        ? Visitor::formatImagePath($record->post_image, 'images/new_name_tags')
                                        : 'https://cairo.beacon.com.eg/storage/nameTags/' . $record->post_image;

                                    return view('filament.components.name-tag-preview', [
                                        'url' => $url,
                                        'label' => 'Social Media Name Tag'
                                    ]);
                                }
                                return 'No name tag generated';
                            }),
                        Forms\Components\Placeholder::make('event_name_tag_preview')
                            ->label('Event Name Tag')
                            ->content(function (Visitor $record) {
                                if ($record->name_tag) {
                                    // Try local path first, then fallback to external URL
                                    $localPath = public_path('images/name_tags/' . $record->name_tag);
                                    $url = file_exists($localPath)
                                        ? Visitor::formatImagePath($record->name_tag, 'images/name_tags')
                                        : 'https://cairo.beacon.com.eg/storage/nameTags/' . $record->name_tag;

                                    return view('filament.components.name-tag-preview', [
                                        'url' => $url,
                                        'label' => 'Event Name Tag'
                                    ]);
                                }
                                return 'No event name tag generated';
                            }),
                    ])->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('image')
                    ->label('Photo')
                    ->formatStateUsing(function ($state, Visitor $record) {
                        if (!$state) {
                            // Use fallback URL when no image is set
                            $fallbackUrl = 'https://adminc.beacon.com.eg/storage/visitors/images/1745277698_Kristin_Burke.png';
                            return '<img src="' . $fallbackUrl . '" class="rounded-full w-10 h-10 object-cover" alt="No Photo" />';
                        }

                        // Try local path first, then fallback to external URL
                        $imageUrl = $record->formatted_image ?? Visitor::formatImagePath($state, 'storage/visitor_images');

                        // If local image doesn't exist, use fallback URL
                        if (!$record->formatted_image && $state) {
                            $localPath = public_path('storage/visitor_images/' . $state);
                            if (!file_exists($localPath)) {
                                $imageUrl = 'https://adminc.beacon.com.eg/storage/visitors/images/' . $state;
                            }
                        }

                        return '<img src="' . $imageUrl . '" class="rounded-full w-10 h-10 object-cover" alt="Visitor Photo" />';
                    })
                    ->html(),
                Tables\Columns\TextColumn::make('first_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('second_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('qr_code')
                    ->label('QR Code')
                    ->limit(15)
                    ->tooltip(fn($record) => $record->qr_code)
                    ->toggleable()
                    ->copyable()
                    ->copyMessage('QR code copied!')
                    ->placeholder('No QR code'),
                Tables\Columns\TextColumn::make('company')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('title1')
                    ->label('Job Title')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('receive_nameTag')
                    ->label('Received Name Tag')
                    ->boolean()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\IconColumn::make('attend')
                    ->label('Attended')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('workshops')
                    ->label('Workshops')
                    ->getStateUsing(function ($record) {
                        $workshops = $record->agendas;
                        if ($workshops->count() === 0) {
                            return 'No workshops';
                        }
                        return $workshops->map(function ($workshop) {
                            $time = $workshop->time ? "({$workshop->time})" : '';
                            return $workshop->name . ' ' . $time;
                        })->join(', ');
                    })
                    ->limit(30)
                    ->tooltip(function ($record) {
                        $workshops = $record->agendas;
                        if ($workshops->count() === 0) {
                            return 'No workshops assigned';
                        }
                        return $workshops->map(function ($workshop) {
                            $time = $workshop->time ? " ({$workshop->time})" : '';
                            $startTime = $workshop->start_from ? " - Starts: {$workshop->start_from}" : '';
                            return $workshop->name . $time . $startTime;
                        })->join("\n");
                    })
                    ->toggleable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\Filter::make('has_name_tag')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('name_tag')),
                Tables\Filters\Filter::make('no_name_tag')
                    ->query(fn(Builder $query): Builder => $query->whereNull('name_tag')),
                Tables\Filters\Filter::make('attended')
                    ->query(fn(Builder $query): Builder => $query->where('attend', true)),
                Tables\Filters\Filter::make('not_attended')
                    ->query(fn(Builder $query): Builder => $query->where('attend', false)),
                Tables\Filters\Filter::make('received_name_tag')
                    ->query(fn(Builder $query): Builder => $query->where('receive_nameTag', true)),
                Tables\Filters\Filter::make('not_received_name_tag')
                    ->query(fn(Builder $query): Builder => $query->where('receive_nameTag', false)),
                Tables\Filters\SelectFilter::make('country')
                    ->options(function () {
                        $countries = json_decode(file_get_contents(resource_path('Data/country.json')), true);
                        $countryOptions = [];
                        foreach ($countries as $country) {
                            $countryOptions[$country['code']] = $country['name'];
                        }
                        return $countryOptions;
                    }),
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from'),
                        Forms\Components\DatePicker::make('created_until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Action::make('download_name_tag')
                    ->label('Download Name Tag')
                    ->icon('heroicon-o-arrow-down-tray')
                    ->color('primary')
                    ->url(function (Visitor $visitor) {
                        if (!$visitor->name_tag) {
                            return null;
                        }

                        // Try local path first, then fallback to external URL
                        $localPath = public_path('images/name_tags/' . $visitor->name_tag);
                        if (file_exists($localPath)) {
                            return Visitor::formatImagePath($visitor->name_tag, 'images/name_tags');
                        } else {
                            // Use fallback URL if local file doesn't exist
                            return 'https://cairo.beacon.com.eg/storage/nameTags/' . $visitor->name_tag;
                        }
                    })
                    ->openUrlInNewTab()
                    ->visible(fn(Visitor $visitor) => !empty($visitor->name_tag)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVisitors::route('/'),
            'view' => Pages\ViewVisitor::route('/{record}'),
            'edit' => Pages\EditVisitor::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
