<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SpeakerResource\Pages;
use App\Models\Ticket;
use App\Models\Visitor;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\Builder;

class SpeakerResource extends Resource
{
    protected static ?string $model = Visitor::class;

    protected static ?string $navigationIcon = 'heroicon-o-microphone';

    protected static ?string $navigationGroup = 'Attendees';

    protected static ?string $navigationLabel = 'Speakers';

    protected static ?string $modelLabel = 'Speaker';

    protected static ?string $pluralModelLabel = 'Speakers';

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()->where('type', 'speaker');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->unique(table: 'visitors', ignoreRecord: true)
                    ->maxLength(255),

                Forms\Components\TextInput::make('phone')
                    ->tel()
                    ->maxLength(255),

                Forms\Components\TextInput::make('whatsapp_number')
                    ->tel()
                    ->maxLength(255),

                Forms\Components\TextInput::make('jop_title')
                    ->label('Job Title')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('company')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('company_category')
                    ->maxLength(255),

                Forms\Components\Textarea::make('bio')
                    ->columnSpanFull(),

                Forms\Components\TextInput::make('linkedin')
                    ->label('LinkedIn Profile')
                    ->url()
                    ->maxLength(255),

                Forms\Components\TextInput::make('facebook')
                    ->label('Facebook Profile')
                    ->url()
                    ->maxLength(255),

                Forms\Components\FileUpload::make('speaker_image')
                    ->label('Profile Image')
                    ->directory('images/speakers')
                    ->disk('public_folder')
                    ->image()
                    ->imageEditor()
                    ->imageEditorAspectRatios(['1:1'])
                    ->preserveFilenames()
                    ->maxSize(512),

                Forms\Components\Select::make('ticket_id')
                    ->label('Associate with Speaker Ticket')
                    ->options(function () {
                        return Ticket::pluck('name', 'id');
                    })
                    ->searchable(),

                Forms\Components\TextInput::make('password')
                    ->password()
                    ->required(fn (string $operation): bool => $operation === 'create')
                    ->dehydrateStateUsing(fn (string $state): string => Hash::make($state))
                    ->dehydrated(fn (?string $state): bool => filled($state))
                    ->maxLength(255),

                Forms\Components\Hidden::make('type')
                    ->default('speaker'),
                Forms\Components\Hidden::make('user_id')
                    ->default(fn () => auth()->id()),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('speaker_image')
                    ->label('Photo')
                    ->disk('public_folder')
                    ->circular(),

                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('email')
                    ->searchable(),

                Tables\Columns\TextColumn::make('jop_title')
                    ->label('Job Title'),

                Tables\Columns\TextColumn::make('company')
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSpeakers::route('/'),
            'create' => Pages\CreateSpeaker::route('/create'),
            'edit' => Pages\EditSpeaker::route('/{record}/edit'),
        ];
    }
}
