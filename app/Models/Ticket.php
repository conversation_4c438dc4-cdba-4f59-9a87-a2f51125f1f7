<?php

namespace App\Models;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Ticket extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = ['id'];

    public function getDiscountAttribute()
    {

        if ($this->discount_start_date <= Carbon::now()->format('Y-m-d H:i:s') && $this->discount_end_date >= Carbon::now()->format('Y-m-d H:i:s')) {
            return $this->discount_price;
        } else {
            return 0;
        }
    }
     public function visitorTickets ()
    {
        return $this->hasMany(VisitorTicket::class);
    }
}
