<?php

namespace App\Models;

use App\Traits\BelongsToEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gallery extends Model
{
    use HasFactory , BelongsToEvent;
    protected $guarded = ['id'];

    public function setImageAttribute($value)
    {
        if (!empty($value)) {
            $filename = $value->getClientOriginalName();
            $location = storage_path('app/public/gallery');
            $value->move($location, $filename);
            $this->attributes['image'] = $filename;
        }
    }
}
