<?php

namespace App\Models;

//use App\Traits\BelongsToEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    use HasFactory ;
    protected $guarded = ['id'];

    public function sponsor() {
        return $this->belongsTo(Sponsor::class)->withDefault();;
    }
    public function visitor() {
        return $this->belongsTo(Visitor::class)->withDefault();
    }
}
