<?php

namespace App\Models;

use App\Traits\BelongsToEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Agenda extends Model
{
    use HasFactory;
    protected $guarded = [];

    public function speakers ()
    {
        return $this->belongsToMany(Visitor::class,'agenda_speakers','agenda_id','speaker_id');
    }
  public function visitors ()
    {
        return $this->belongsToMany(Visitor::class,'visitor_agendas','agenda_id','visitor_id')->withPivot('attend','confirmed');
    }
}
