<?php

namespace App\Models;

use App\Http\Controllers\Admin\TicketController;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Event extends Model
{
    use HasFactory,SoftDeletes;
    protected $guarded = ['id'];

    public function setLogoAttribute($value)
    {
        if (!empty($value)) {
            $filename = $value->getClientOriginalName();
            $location = storage_path('app/public/events');
            $value->move($location, $filename);
            $this->attributes['logo'] = $filename;
        }
    }
    public function invoices() {
        return $this->hasMany(Invoice::class)->OrderBy('created_at','desc');
    }
    public function tickets() {
        return $this->hasMany(Ticket::class);
    }
    public function agenda () {
        return $this->hasMany(Agenda::class);
    }
    public function faqs() {
        return $this->hasMany(Faq::class);
    }

    public function plans() {
        return $this->hasMany(Plan::class);
    }

    public function sponsors() {
        return $this->hasMany(Sponsor::class);
    }

    public function gallery() {
        return $this->hasMany(Gallery::class);
    }
}
