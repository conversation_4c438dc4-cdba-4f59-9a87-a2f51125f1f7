<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Slider extends Model
{
    use HasFactory;

    protected $guarded = ['id'];

    public function setImageAttribute($value){
        if (!empty($value)) {
            $filename = $value->getClientOriginalName();
            $location = storage_path('app/public/slider');
            $value->move($location, $filename);

            $this->attributes['image'] = $filename;
        }
    }
}
