<?php

namespace App\Models;

use App\Traits\BelongsToEvent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VisitorTicket extends Model
{
    use HasFactory;
    protected $guarded = ['id'];

    public function ticket ()
    {
        return $this->belongsTo(Ticket::class);
    }
    public function visitor ()
    {
        return $this->belongsTo(Visitor::class)->withDefault();
    }
     public function user()
    {
        return $this->belongsTo(User::class)->withDefault();
    }
    
     public function visitors ()
    {
        return $this->hasMany(Visitor::class,'corporate_code','code');
    }
}
