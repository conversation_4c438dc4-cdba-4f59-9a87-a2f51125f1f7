<?php

namespace App\Http\Requests;

use App\Models\Visitor;
use Illuminate\Auth\Events\Verified;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class VisitorEmailVerificationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $visitor = Visitor::find($this->route('id'));

        if (!$visitor) {
            return false;
        }

        if (! hash_equals(sha1($visitor->getEmailForVerification()), (string) $this->route('hash'))) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //
        ];
    }

    /**
     * Fulfill the email verification request.
     *
     * @return void
     */
    public function fulfill()
    {
        $visitor = Visitor::find($this->route('id'));

        if (! $visitor->hasVerifiedEmail()) {
            $visitor->markEmailAsVerified();

            event(new Verified($visitor));
        }
    }
}
