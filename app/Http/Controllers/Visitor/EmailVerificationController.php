<?php

namespace App\Http\Controllers\Visitor;

use App\Http\Controllers\Controller;
use App\Http\Requests\VisitorEmailVerificationRequest;
use Illuminate\Http\Request;

class EmailVerificationController extends Controller
{
    /**
     * Show the email verification notice.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function notice(Request $request)
    {
        return view('visitor.verify-email');
    }

    /**
     * Mark the authenticated user's email address as verified.
     *
     * @param  \App\Http\Requests\VisitorEmailVerificationRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function verify(VisitorEmailVerificationRequest $request)
    {
        $request->fulfill();
        return redirect()->route('visitor.ticket')->with('success', 'Email verified successfully!');
    }

    /**
     * Resend the email verification notification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resend(Request $request)
    {
        $request->user('visitor')->sendEmailVerificationNotification();
        return back()->with('message', 'Verification link sent!');
    }
}
