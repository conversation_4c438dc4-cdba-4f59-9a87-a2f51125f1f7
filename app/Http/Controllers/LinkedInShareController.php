<?php

namespace App\Http\Controllers;

use App\Models\Visitor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Carbon\Carbon;

class LinkedInShareController extends Controller
{
    private string $authUrl = 'https://www.linkedin.com/oauth/v2/authorization';
    private string $tokenUrl = 'https://www.linkedin.com/oauth/v2/accessToken';
    private string $profileUrl = 'https://api.linkedin.com/v2/userinfo';
    private string $uploadUrl = 'https://api.linkedin.com/v2/assets?action=registerUpload';
    private string $postUrl = 'https://api.linkedin.com/v2/ugcPosts';

    /**
     * Generate a secure sharing link for a visitor
     */
    public function generateShareLink(Visitor $visitor)
    {
        // Generate a secure token
        $token = Str::random(64);
        $expiresAt = Carbon::now()->addDays(30); // Token expires in 30 days

        // Update visitor with the token
        $visitor->update([
            'linkedin_share_token' => $token,
            'linkedin_share_token_expires_at' => $expiresAt,
        ]);

        // Generate the direct sharing URL
        $shareUrl = route('linkedin.direct-share', ['token' => $token]);

        return response()->json([
            'success' => true,
            'share_url' => $shareUrl,
            'expires_at' => $expiresAt->format('Y-m-d H:i:s'),
        ]);
    }

    /**
     * Handle direct LinkedIn sharing via token
     */
    public function directShare(Request $request, $token)
    {
        // Find visitor by token
        $visitor = Visitor::where('linkedin_share_token', $token)
            ->where('linkedin_share_token_expires_at', '>', Carbon::now())
            ->first();

        if (!$visitor) {
            return view('linkedin.share-error', [
                'error' => 'Invalid or expired sharing link. Please contact the event organizer for a new link.'
            ]);
        }

        if (!$visitor->name_tag && !$visitor->post_image) {
            return view('linkedin.share-error', [
                'error' => 'No name tag found for this visitor. Please ensure your name tag has been generated.'
            ]);
        }

        // Start session if not already started
        if (!Session::isStarted()) {
            Session::start();
        }

        \Log::info('Starting LinkedIn share process', [
            'visitor_id' => $visitor->id,
            'session_id' => Session::getId(),
            'session_started' => Session::isStarted()
        ]);

        // Store visitor ID in session for callback with explicit save
        Session::put('share_linkedin_visitor_id', $visitor->id);
        Session::put('share_linkedin_token', $token);
        Session::save(); // Force session save before redirect

        // Generate state parameter that includes encrypted visitor data as backup
        $stateData = [
            'visitor_id' => $visitor->id,
            'token' => $token,
            'timestamp' => time()
        ];
        $encryptedState = encrypt(json_encode($stateData));

        // Store state in session as primary method
        Session::put('linkedin_oauth_state', $encryptedState);
        Session::save(); // Force session save

        \Log::info('Session data stored for LinkedIn OAuth', [
            'visitor_id' => $visitor->id,
            'token' => $token,
            'state_length' => strlen($encryptedState),
            'session_id' => Session::getId()
        ]);

        // Build LinkedIn OAuth URL with encrypted state
        $params = [
            'response_type' => 'code',
            'client_id' => config('services.linkedin.client_id'),
            'redirect_uri' => route('linkedin.direct-callback'),
            'scope' => 'openid profile w_member_social',
            'state' => $encryptedState,
        ];

        return redirect()->away($this->authUrl . '?' . http_build_query($params));
    }

    /**
     * Handle LinkedIn OAuth callback for direct sharing
     */
    public function directCallback(Request $request)
    {
        \Log::info('LinkedIn callback received', [
            'has_code' => $request->has('code'),
            'has_error' => $request->has('error'),
            'state' => $request->get('state'),
            'session_id' => Session::getId(),
            'session_data' => [
                'visitor_id' => Session::get('share_linkedin_visitor_id'),
                'token' => Session::get('share_linkedin_token'),
                'stored_state' => Session::get('linkedin_oauth_state')
            ]
        ]);

        if ($request->has('error')) {
            return view('linkedin.share-error', [
                'error' => 'LinkedIn authorization failed: ' . $request->get('error_description', 'Unknown error')
            ]);
        }

        if (!$request->has('code')) {
            return view('linkedin.share-error', [
                'error' => 'No authorization code received from LinkedIn.'
            ]);
        }

        // Get visitor data from session or encrypted state parameter
        $visitorId = Session::get('share_linkedin_visitor_id');
        $token = Session::get('share_linkedin_token');
        $receivedState = $request->get('state');

        // If session data is missing, try to decrypt from state parameter
        if ((!$visitorId || !$token) && $receivedState) {
            \Log::info('Session data missing, attempting to decrypt from state parameter');
            try {
                $decryptedData = json_decode(decrypt($receivedState), true);
                if ($decryptedData && isset($decryptedData['visitor_id'], $decryptedData['token'])) {
                    // Check if the state is not too old (max 1 hour)
                    if (time() - $decryptedData['timestamp'] < 3600) {
                        $visitorId = $decryptedData['visitor_id'];
                        $token = $decryptedData['token'];
                        \Log::info('Successfully recovered data from state parameter', [
                            'visitor_id' => $visitorId
                        ]);
                    } else {
                        \Log::warning('State parameter too old', [
                            'age' => time() - $decryptedData['timestamp']
                        ]);
                    }
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to decrypt state parameter', [
                    'error' => $e->getMessage()
                ]);
            }
        }

        \Log::info('Final visitor data', [
            'visitor_id' => $visitorId,
            'token' => $token ? 'present' : 'missing',
            'source' => $visitorId ? ($token ? 'session_or_state' : 'unknown') : 'none'
        ]);

        if (!$visitorId || !$token) {
            return view('linkedin.share-error', [
                'error' => 'Session expired. Please use the sharing link again.'
            ]);
        }

        // Verify token is still valid
        $visitor = Visitor::where('id', $visitorId)
            ->where('linkedin_share_token', $token)
            ->where('linkedin_share_token_expires_at', '>', Carbon::now())
            ->first();

        if (!$visitor) {
            return view('linkedin.share-error', [
                'error' => 'Invalid or expired sharing link.'
            ]);
        }

        try {
            // Increase execution time for LinkedIn API operations
            set_time_limit(120);

            \Log::info('Step 1: Exchanging code for access token');
            $accessToken = $this->exchangeCodeForToken($request->get('code'));

            \Log::info('Step 2: Fetching LinkedIn profile ID');
            $profileId = $this->fetchLinkedInProfileId($accessToken);

            // Use post_image if available, otherwise use name_tag
            $imageUrl = $visitor->post_image ?: $visitor->name_tag;

            \Log::info('Step 3: LinkedIn sharing process started', [
                'visitor_id' => $visitor->id,
                'image_url' => $imageUrl,
                'post_image' => $visitor->post_image,
                'name_tag' => $visitor->name_tag,
                'profile_id' => $profileId
            ]);

            // Test image accessibility before proceeding
            \Log::info('Step 3.5: Testing image accessibility');
            $testImageContent = $this->getImageContent($imageUrl);
            if (!$testImageContent) {
                throw new \Exception('Image not accessible before LinkedIn upload. URL: ' . $imageUrl);
            }
            \Log::info('Image accessibility test passed', ['image_size' => strlen($testImageContent)]);

            \Log::info('Step 4: Uploading image to LinkedIn');
            $assetUrn = $this->uploadImageToLinkedIn($accessToken, $profileId, $imageUrl);

            \Log::info('Step 5: Creating LinkedIn post');
            $this->createImagePost($accessToken, $profileId, $assetUrn, $visitor);

            \Log::info('Step 6: Updating visitor record');
            // Update visitor
            $visitor->update(['posted_at' => now()]);

            // Invalidate the token after successful use
            $visitor->update([
                'linkedin_share_token' => null,
                'linkedin_share_token_expires_at' => null,
            ]);

            // Clear session data after successful completion
            Session::forget(['share_linkedin_visitor_id', 'share_linkedin_token', 'linkedin_oauth_state']);

            \Log::info('LinkedIn sharing completed successfully', ['visitor_id' => $visitor->id]);

            return view('linkedin.share-success', [
                'visitor' => $visitor,
                'message' => 'Your name tag has been successfully shared on LinkedIn!'
            ]);

        } catch (\Throwable $e) {
            \Log::error('LinkedIn direct share error', [
                'visitor_id' => $visitor->id,
                'visitor_post_image' => $visitor->post_image,
                'visitor_name_tag' => $visitor->name_tag,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return view('linkedin.share-error', [
                'error' => 'Failed to share on LinkedIn: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Exchange authorization code for access token
     */
    private function exchangeCodeForToken(string $code): string
    {
        $response = Http::timeout(30)->asForm()->post($this->tokenUrl, [
            'grant_type' => 'authorization_code',
            'code' => $code,
            'redirect_uri' => route('linkedin.direct-callback'),
            'client_id' => config('services.linkedin.client_id'),
            'client_secret' => config('services.linkedin.client_secret'),
        ]);

        if (!$response->successful()) {
            throw new \Exception('Failed to exchange code for token: ' . $response->body());
        }

        $data = $response->json();
        return $data['access_token'];
    }

    /**
     * Fetch LinkedIn profile ID
     */
    private function fetchLinkedInProfileId(string $accessToken): string
    {
        $response = Http::timeout(30)->withToken($accessToken)->get($this->profileUrl);

        if (!$response->successful()) {
            throw new \Exception('Failed to fetch LinkedIn profile: ' . $response->body());
        }

        $data = $response->json();
        return $data['sub'];
    }

    /**
     * Upload image to LinkedIn
     */
    private function uploadImageToLinkedIn(string $accessToken, string $profileId, string $imageUrl): string
    {
        // Register upload
        $registerResponse = Http::timeout(30)->withToken($accessToken)->post($this->uploadUrl, [
            'registerUploadRequest' => [
                'recipes' => ['urn:li:digitalmediaRecipe:feedshare-image'],
                'owner' => "urn:li:person:{$profileId}",
                'serviceRelationships' => [
                    [
                        'relationshipType' => 'OWNER',
                        'identifier' => 'urn:li:userGeneratedContent'
                    ]
                ]
            ]
        ]);

        if (!$registerResponse->successful()) {
            throw new \Exception('Failed to register upload: ' . $registerResponse->body());
        }

        $registerData = $registerResponse->json();
        $uploadUrl = $registerData['value']['uploadMechanism']['com.linkedin.digitalmedia.uploading.MediaUploadHttpRequest']['uploadUrl'];
        $asset = $registerData['value']['asset'];

        // Get image content - handle both URLs and file paths
        $imageContent = $this->getImageContent($imageUrl);
        if (!$imageContent) {
            throw new \Exception('Failed to read image file from any location. Original path: ' . $imageUrl . '. Please check the logs for detailed path attempts.');
        }

        // Upload image with timeout
        $uploadResponse = Http::timeout(60)->withBody($imageContent, 'image/png')->put($uploadUrl);

        if (!$uploadResponse->successful()) {
            throw new \Exception('Failed to upload image: ' . $uploadResponse->body());
        }

        return $asset;
    }

    /**
     * Create LinkedIn post with image
     */
    private function createImagePost(string $accessToken, string $profileId, string $assetUrn, Visitor $visitor): void
    {
        $eventName = config('app.name', 'Future of Finance MEA | Cairo 2025');
        $text = "Excited to be attending {$eventName}! Looking forward to connecting with industry leaders and exploring the future of finance. #FutureOfFinance #Cairo2025 #Finance #Networking";

        $postData = [
            'author' => "urn:li:person:{$profileId}",
            'lifecycleState' => 'PUBLISHED',
            'specificContent' => [
                'com.linkedin.ugc.ShareContent' => [
                    'shareCommentary' => [
                        'text' => $text
                    ],
                    'shareMediaCategory' => 'IMAGE',
                    'media' => [
                        [
                            'status' => 'READY',
                            'description' => [
                                'text' => "My name tag for {$eventName}"
                            ],
                            'media' => $assetUrn,
                            'title' => [
                                'text' => "{$eventName} - {$visitor->name}"
                            ]
                        ]
                    ]
                ]
            ],
            'visibility' => [
                'com.linkedin.ugc.MemberNetworkVisibility' => 'PUBLIC'
            ]
        ];

        $response = Http::timeout(30)->withToken($accessToken)->post($this->postUrl, $postData);

        if (!$response->successful()) {
            throw new \Exception('Failed to create LinkedIn post: ' . $response->body());
        }
    }

    /**
     * Get image content from various sources (URL, file path, etc.)
     */
    private function getImageContent(string $imageUrl): string|false
    {
        \Log::info('Attempting to get image content', ['imageUrl' => $imageUrl]);

        // Extract filename from URL if it's a full URL
        $filename = basename(parse_url($imageUrl, PHP_URL_PATH));

        // Priority 1: Try local file paths first (faster and more reliable)
        $possiblePaths = [
            // Name tags directory (classic name tags)
            public_path('images/name_tags/' . $filename),

            // New name tags directory (post_image)
            public_path('images/new_name_tags/' . $filename),

            // Storage paths
            storage_path('app/public/images/name_tags/' . $filename),
            storage_path('app/public/images/new_name_tags/' . $filename),

            // Visitor images
            storage_path('app/public/visitor_images/' . $filename),
            public_path('storage/visitor_images/' . $filename),
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path) && is_readable($path)) {
                \Log::info('Successfully found image at local path', ['path' => $path]);
                return file_get_contents($path);
            }
        }

        // Priority 2: If it's a full URL, try to fetch it directly
        if (filter_var($imageUrl, FILTER_VALIDATE_URL)) {
            \Log::info('Attempting to fetch image from URL', ['url' => $imageUrl]);
            try {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 20,
                        'method' => 'GET',
                        'header' => 'User-Agent: Mozilla/5.0 (compatible; LinkedIn Share Bot)',
                        'ignore_errors' => true
                    ]
                ]);
                $content = file_get_contents($imageUrl, false, $context);
                if ($content !== false && strlen($content) > 0) {
                    \Log::info('Successfully fetched image from original URL');
                    return $content;
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to fetch from original URL', ['error' => $e->getMessage()]);
            }
        }

        // Priority 3: Try constructing URLs as last resort
        $baseUrl = config('app.url', 'http://127.0.0.1:8000');
        $urlsToTry = [
            $baseUrl . '/images/name_tags/' . $filename,
            $baseUrl . '/images/new_name_tags/' . $filename,
        ];

        foreach ($urlsToTry as $url) {
            try {
                $context = stream_context_create([
                    'http' => [
                        'timeout' => 10,
                        'method' => 'GET',
                        'ignore_errors' => true
                    ]
                ]);
                $content = file_get_contents($url, false, $context);
                if ($content !== false && strlen($content) > 0) {
                    \Log::info('Successfully fetched image from constructed URL', ['url' => $url]);
                    return $content;
                }
            } catch (\Exception $e) {
                \Log::warning('Failed to fetch from constructed URL', ['url' => $url, 'error' => $e->getMessage()]);
            }
        }

        \Log::error('Failed to find image at any location', [
            'originalUrl' => $imageUrl,
            'filename' => $filename,
            'pathsChecked' => count($possiblePaths),
            'urlsChecked' => count($urlsToTry) + 1
        ]);
        return false;
    }
}
