<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckSessionExpiration
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if user is authenticated and session has expired
        if (Auth::check()) {
            $sessionLifetime = config('session.lifetime') * 60; // Convert minutes to seconds
            $lastActivity = session('last_activity', time());

            if (time() - $lastActivity > $sessionLifetime) {
                // Session expired - clear session and redirect
                $request->session()->flush();
                $request->session()->invalidate();
                $request->session()->regenerateToken();

                // Use the 'login' named route which is now properly aliased
                return redirect()->route('visitor.login')->with('message', 'Your session has expired. Please log in again.');
            }

            // Update last activity time
            session(['last_activity' => time()]);
        }

        return $next($request);
    }
}
