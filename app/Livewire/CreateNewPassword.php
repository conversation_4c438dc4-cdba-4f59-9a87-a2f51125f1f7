<?php

namespace App\Livewire;

use Illuminate\Support\Facades\View;
use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Visitor;
use Carbon\Carbon;

class CreateNewPassword extends Component
{
    public $email = '';
    public $token = '';
    public $password = '';
    public $password_confirmation = '';

    protected $rules = [
        'password' => 'required|min:8|confirmed',
        'password_confirmation' => 'required|same:password'
    ];

    protected $messages = [
        'password.required' => 'Please enter a new password.',
        'password.min' => 'Password must be at least 8 characters.',
        'password.confirmed' => 'Password confirmation does not match.',
        'password_confirmation.required' => 'Please confirm your password.'
    ];

    public function mount()
    {
        $this->token = request()->query('token');
        $this->email = request()->query('email');

        Log::info('CreateNewPassword mounted', ['email' => $this->email, 'token' => $this->token]);

        if (!$this->token || !$this->email) {
            session()->flash('error', 'Invalid password reset link.');
            return redirect()->route('visitor.login');
        }

        // Check if token exists and is valid
        $resetRecord = DB::table('password_resets')
            ->where('email', $this->email)
            ->where('token', $this->token)
            ->first();

        if (!$resetRecord) {
            Log::error('Invalid reset record', ['email' => $this->email, 'token' => $this->token]);
            session()->flash('error', 'Invalid or expired password reset link.');
            return redirect()->route('visitor.login');
        }

        // Check if token is expired
        $expiresAt = Carbon::parse($resetRecord->expires_at ?? Carbon::now()->subMinutes(1));

        if (Carbon::now()->isAfter($expiresAt)) {
            Log::error('Expired token', ['email' => $this->email, 'expires_at' => $expiresAt]);
            session()->flash('error', 'Your password reset link has expired. Please request a new one.');
            return redirect()->route('visitor.login');
        }
    }

    public function updatePassword()
    {
        $this->validate();
        $visitor = Visitor::where('email', $this->email)->first();

        if (!$visitor) {
            Log::error('Visitor not found during password reset', ['email' => $this->email]);
            session()->flash('error', 'User not found.');
            return redirect()->route('visitor.login');
        }
        $visitor->password = Hash::make($this->password);
        $visitor->save();
        DB::table('password_resets')
            ->where('email', $this->email)
            ->delete();
        session()->flash('success', 'Your password has been reset successfully. Please login with your new password.');
        return redirect()->route('visitor.login');
    }

    public function backToLogin()
    {
        return redirect()->route('visitor.login');
    }

    public function render()
    {
        return view('livewire.create-new-password');
    }
}
