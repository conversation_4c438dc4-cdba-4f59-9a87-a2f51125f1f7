<?php

namespace App\Livewire;

use Livewire\Component;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class VisitorLogin extends Component
{
    public $email = '';
    public $password = '';
    public $remember = false;
    public $passwordVisible = false;
    public $successMessage = '';
    public $errorMessage = '';

    protected $rules = [
        'email' => 'required|email',
        'password' => 'required|min:8',
    ];

    protected $messages = [
        'email.required' => 'Please enter your email address.',
        'email.email' => 'Please enter a valid email address.',
        'password.required' => 'Please enter your password.',
        'password.min' => 'Password must be at least 8 characters.',
    ];

    public function mount()
    {
        $this->email = '';
        $this->password = '';
        $this->successMessage = '';
        $this->errorMessage = '';
    }

    public function login()
    {
        $this->validate();

        // Clear any previous error messages
        $this->errorMessage = '';

        // Attempt login with remember me functionality
        if (Auth::guard('visitor')->attempt([
            'email' => $this->email,
            'password' => $this->password
        ], $this->remember)) {

            // Regenerate session for security
            request()->session()->regenerate();

            // Set success message
            $this->successMessage = 'Login successful! Welcome back.';

            // Redirect to intended page or ticket page
            return redirect()->intended(route('visitor.ticket'));
        }

        // Set error message for failed login
        $this->errorMessage = 'Invalid email or password. Please check your credentials and try again.';
    }

    public function render()
    {
        return view('livewire.visitor-login');
    }
}
