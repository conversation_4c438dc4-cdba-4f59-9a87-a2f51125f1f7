<?php

namespace App\Livewire;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\View\View;
use Livewire\Component;
use Carbon\Carbon;

class VerifyNewPassword extends Component
{
    public $email;
    public $token;
    public $otpDigits = ['', '', '', '',];
    public $password = '';
    public $password_confirmation = '';
    public $otp = '';
    public $otpVerified = false;
    public $otpExpiry = null;

    protected $rules = [
        'otpDigits.0' => 'required|numeric|min:0|max:9',
        'otpDigits.1' => 'required|numeric|min:0|max:9',
        'otpDigits.2' => 'required|numeric|min:0|max:9',
        'otpDigits.3' => 'required|numeric|min:0|max:9',
        'password' => 'required|min:8|confirmed',
        'password_confirmation' => 'required'
    ];

    protected $messages = [
        'otpDigits.*.required' => 'All OTP digits are required.',
        'otpDigits.*.numeric' => 'OTP must contain only numbers.',
        'otpDigits.*.min' => 'OTP digits must be between 0 and 9.',
        'otpDigits.*.max' => 'OTP digits must be between 0 and 9.',
        'password.required' => 'Password is required.',
        'password.min' => 'Password must be at least 8 characters.',
        'password.confirmed' => 'Password confirmation does not match.',
        'password_confirmation.required' => 'Please confirm your password.'
    ];

    public function mount($token = null, $email = null): void
    {
        $this->token = $token ?? request()->query('token');
        $this->email = $email ?? request()->query('email');
        // Check if token exists and is valid
        $resetRecord = DB::table('password_resets')
            ->where('email', $this->email)
            ->where('token', $this->token)
            ->first();

        if (!$resetRecord) {
            Log::error('Invalid reset record', ['email' => $this->email, 'token' => $this->token]);
            session()->flash('error', 'Invalid or expired password reset link.');
            return;
        }

        // Check if token is expired
        $expiresAt = Carbon::parse($resetRecord->expires_at ?? Carbon::now()->subMinutes(1));

        if (Carbon::now()->isAfter($expiresAt)) {
            Log::error('Expired token', ['email' => $this->email, 'expires_at' => $expiresAt]);
            session()->flash('error', 'Your password reset link has expired. Please request a new one.');
            return;
        }

        $this->otpExpiry = $expiresAt;
    }

    public function verifyOTP()
    {
        $this->resetErrorBag();
        // Validate OTP digits
        $this->validate([
            'otpDigits.0' => 'required|numeric|min:0|max:9',
            'otpDigits.1' => 'required|numeric|min:0|max:9',
            'otpDigits.2' => 'required|numeric|min:0|max:9',
            'otpDigits.3' => 'required|numeric|min:0|max:9',
        ]);

        // Combine the OTP digits into a single string
        $this->otp = implode('', $this->otpDigits);



        // Check if OTP matches the one stored in the database
        $resetRecord = DB::table('password_resets')
            ->where('email', $this->email)
            ->where('token', $this->token)
            ->first();

        if (!$resetRecord || $this->otp !== $resetRecord->otp) {
            $this->addError('otp', 'Invalid verification code. Please check and try again.');
            return;
        }

        // Check if OTP is expired
        $expiresAt = Carbon::parse($resetRecord->expires_at ?? Carbon::now()->subMinutes(1));

        if (Carbon::now()->isAfter($expiresAt)) {
            $this->addError('otp', 'Verification code has expired. Please request a new one.');
            return;
        }

        // OTP is valid, allow password reset
        $this->otpVerified = true;

        // Dispatch browser event
        $this->dispatch('otpVerified');

        // Redirect to create new password page
        return redirect()->route('visitor.reset-password.create-password', [
            'token' => $this->token,
            'email' => $this->email
        ]);
    }

    public function resendOTP()
    {
        return redirect()->route('visitor.reset-password')->with([
            'message' => 'Please request a new verification code.'
        ]);
    }

    public function backToLogin()
    {
        return redirect()->route('visitor.login');
    }

    public function render(): View
    {
        return view('livewire.verify-new-password');
    }
}
