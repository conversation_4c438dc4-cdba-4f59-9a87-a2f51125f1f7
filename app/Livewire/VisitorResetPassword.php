<?php

namespace App\Livewire;

use App\Models\Visitor;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Mail\OtpMail;
use Random\RandomException;

class VisitorResetPassword extends Component
{
    public $email = '';
    public $otp = '';
    public $otpSent = false;
    public $otpExpiry = null;

    protected array $rules = [
        'email' => 'required|email|exists:visitors,email',
    ];

    protected array $messages = [
        'email.required' => 'Please enter your email address.',
        'email.email' => 'Please enter a valid email address.',
        'email.exists' => 'We couldn\'t find a visitor with that email address.',
    ];

    public function mount(): void
    {
        $this->email = '';
        $this->otp = '';
        $this->otpSent = false;
    }

    /**
     * @throws RandomException
     */
    public function sendOTP()
    {
        $this->resetErrorBag();

        $validated = $this->validate([
            'email' => 'required|email|exists:visitors,email',
        ]);

        $visitor = Visitor::where('email', $this->email)->first();

        $otp = str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT);

        $expiryTime = Carbon::now()->addMinutes(15);

        DB::table('password_resets')
            ->where('email', $this->email)
            ->delete();

        $token = hash('sha256', $visitor->id . time() . Str::random(40));

        DB::table('password_resets')->insert([
            'email' => $this->email,
            'token' => $token,
            'otp' => $otp,
            'created_at' => Carbon::now(),
            'expires_at' => $expiryTime,
        ]);

        try {

            Mail::to($this->email)->send(new OtpMail($otp));

            $this->otpSent = true;

            $this->otpExpiry = $expiryTime;

            session()->flash('message', 'OTP code has been sent to your email address.');

            $this->dispatch('otpSent');

            return redirect()->route('visitor.reset-password.verify', [
                'email' => $this->email,
                'token' => $token
            ]);

        } catch (\Exception $e) {
            session()->flash('error', 'Failed to send OTP. Please try again.');
            Log::error('Failed to send OTP: ' . $e->getMessage());
        }
    }

    public function render()
    {
        return view('livewire.visitor-reset-password');
    }
}
