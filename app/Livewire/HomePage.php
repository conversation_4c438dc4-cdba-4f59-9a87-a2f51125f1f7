<?php

namespace App\Livewire;

use App\Models\Agenda;
use App\Models\Faq;
use App\Models\Slider;
use App\Models\Sponsor;
use App\Models\Ticket;
use App\Models\Visitor;
use Livewire\Component;

class HomePage extends Component
{
    public $speakers;
    public $sliders;
    public $sponsors;
    public $agendas;
    public $tickets;
    public $faqs;

    public function mount()
    {
        $this->sliders = Slider::all();
        $this->speakers = Visitor::where('type', 'speaker')->get();
        $this->sponsors = Sponsor::all();
        $this->agendas = Agenda::all();
        $this->tickets = Ticket::where('show', 1)->get();
        $this->faqs = Faq::all();
    }

    public function render()
    {
        return view('livewire.home-page', [
            'sponsorsByTier' => $this->sponsors->groupBy('type'),
        ]);
    }
}
