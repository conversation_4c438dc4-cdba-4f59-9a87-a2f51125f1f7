<?php

use Illuminate\Support\Facades\Cache;

if (! function_exists('setting')) {
    /**
     * Retrieve a setting value from the database or cache.
     *
     * Example: setting('about');
     */
    function setting(string $key, $default = null): mixed
    {
        return Cache::rememberForever("setting_{$key}", function () use ($key, $default) {
            return \DB::table('settings')->where('key', $key)->value('value') ?? $default;
        });
    }
}
