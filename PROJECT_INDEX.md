# Cairo Beacon Event - Project Index

## Project Overview
A Laravel-based event management system for the Cairo Beacon Event, featuring visitor registration, payment processing, name tag generation, and social sharing capabilities.

## Technology Stack
- **Framework**: <PERSON><PERSON> (PHP)
- **Frontend**: Livewire, Bootstrap 5, Alpine.js
- **Authentication**: <PERSON><PERSON> Sanctum
- **Database**: MySQL (configured for database sessions)
- **Payments**: Stripe integration
- **UI Components**: SweetAlert2, CropperJS
- **Build Tools**: Vite

## Core Features
- ✅ Visitor registration and authentication
- ✅ Email verification system
- ✅ Payment processing with Stripe
- ✅ Dynamic name tag generation
- ✅ LinkedIn OAuth integration
- ✅ Session management with auto-logout
- ✅ Responsive design with Bootstrap
- ✅ Image cropping functionality

## Directory Structure

### `/app` - Application Logic
```
app/
├── Filament/           # Admin panel components
├── Http/
│   ├── Controllers/    # Traditional controllers
│   ├── Middleware/     # Custom middleware including CheckSessionExpiration
│   └── Requests/       # Form request validation
├── Livewire/          # Livewire components
│   ├── HomePage.php
│   ├── VisitorLogin.php
│   ├── VisitorRegister.php
│   ├── VisitorNameTag.php
│   ├── PaymentProcess.php
│   └── ShareOnLinkedin.php
├── Models/            # Eloquent models
├── Services/          # Business logic services
├── Mail/             # Email templates and classes
└── Providers/        # Service providers
```

### `/config` - Configuration Files
```
config/
├── app.php           # Application settings
├── auth.php          # Authentication guards and providers
├── database.php      # Database connections
├── session.php       # Session configuration (120min lifetime)
├── services.php      # Third-party services (Stripe)
└── site.php          # Site-specific settings
```

### `/resources` - Frontend Assets
```
resources/
├── css/              # Stylesheets
├── js/               # JavaScript files
├── views/
│   ├── components/   # Blade components
│   │   └── layouts/
│   │       └── app.blade.php  # Main layout with session management
│   ├── livewire/     # Livewire component views
│   └── mail/         # Email templates
└── Data/             # JSON data files (countries, etc.)
```

### `/routes` - Route Definitions
```
routes/
└── web.php           # Web routes with session expiration middleware
```

### `/public` - Public Assets
```
public/
├── images/           # Static images and logos
├── css/             # Compiled CSS
├── js/              # Compiled JavaScript
└── index.php        # Entry point
```

### `/database` - Database Related
```
database/
├── migrations/      # Database migrations
└── seeders/         # Database seeders
```

## Key Components

### Authentication System
- **Guards**: `web` (admin), `sanctum` (visitors)
- **Models**: `User`, `Visitor`
- **Routes**: Login, register, password reset with email verification
- **Middleware**: Session expiration checking

### Livewire Components
1. **HomePage.php** - Landing page
2. **VisitorLogin.php** - Visitor authentication
3. **VisitorRegister.php** - Registration with country selection
4. **VisitorNameTag.php** - Name tag customization with image cropping
5. **PaymentProcess.php** - Stripe payment integration
6. **ShareOnLinkedin.php** - LinkedIn OAuth callback

### Session Management
- **Middleware**: `CheckSessionExpiration` - Automatic logout on expiration
- **Frontend**: JavaScript-based activity tracking and warnings
- **Configuration**: 120-minute sessions with 5-minute warnings
- **Features**: Activity-based extension, AJAX/Livewire support

### Payment Processing
- **Integration**: Stripe for payment processing
- **Configuration**: API keys in `config/services.php`
- **Flow**: Visitor → Registration → Payment → Name Tag

### Name Tag System
- **Generator**: Custom name tag generation with visitor photos
- **Features**: Image cropping, template overlay, caching
- **Storage**: Generated tags stored in public directory

## Environment Configuration

### Required Environment Variables
```env
# App
APP_NAME="Cairo Beacon Event"
APP_ENV=production
APP_KEY=base64:...
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=cairo_beacon_event
DB_USERNAME=your_username
DB_PASSWORD=your_password

# Session
SESSION_DRIVER=database
SESSION_LIFETIME=120

# Stripe
STRIPE_KEY=pk_...
STRIPE_SECRET=sk_...

# Mail
MAIL_MAILER=smtp
MAIL_HOST=...
MAIL_PORT=587
MAIL_USERNAME=...
MAIL_PASSWORD=...
```

## Development Setup
1. Clone repository
2. Run `composer install`
3. Copy `.env.example` to `.env`
4. Configure database and services
5. Run `php artisan migrate`
6. Run `php artisan key:generate`
7. Run `npm install && npm run build`
8. Configure web server to point to `/public`

## API Endpoints

### Authentication Routes
- `GET /` - Home page
- `GET /register` - Visitor registration
- `GET /login` - Visitor login
- `POST /logout` - Logout
- `GET /extend-session` - Session extension (AJAX)

### Protected Routes (requires authentication)
- `GET /visitor/ticket` - Payment processing
- `GET /visitor/linkedin/callback` - LinkedIn OAuth
- `GET /visitor/email/verify` - Email verification

## Security Features
- ✅ CSRF protection
- ✅ Session-based authentication
- ✅ Email verification
- ✅ Rate limiting on sensitive routes
- ✅ Signed URLs for verification
- ✅ Automatic session expiration
- ✅ Activity-based session extension

## Deployment Notes
- Configure web server document root to `/public`
- Set up SSL certificate
- Configure email delivery
- Set up database with proper user permissions
- Enable caching for production performance
- Configure session storage (database recommended)

## Recent Updates
- ✅ Added comprehensive session management system
- ✅ Implemented automatic logout on session expiration
- ✅ Added session warning system with 5-minute notice
- ✅ Activity-based session extension
- ✅ AJAX and Livewire session expiration handling
- ✅ Enhanced security with middleware protection

## Support Documentation
- `SESSION_MANAGEMENT.md` - Detailed session system documentation
- `development-documentation.md` - Development notes
- `documentation.md` - General project documentation
