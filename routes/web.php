<?php

use App\Livewire\HomePage;
use App\Livewire\VisitorTicket;
use App\Livewire\VisitorLogin;
use App\Livewire\VisitorResetPassword;
use App\Livewire\VerifyNewPassword;
use App\Livewire\CreateNewPassword;
use Illuminate\Support\Facades\Route;
use App\Livewire\VisitorRegister;
use App\Http\Controllers\Visitor\EmailVerificationController;
use App\Http\Controllers\NameTagController;
use App\Livewire\PaymentProcess;
use App\Livewire\ShareOnLinkedin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Public / guest pages
|--------------------------------------------------------------------------
*/
Route::get('/',                            HomePage::class)->name('home');

Route::post('/logout', function (Request $request) {
    Auth::logout();
    $request->session()->invalidate();
    $request->session()->regenerateToken();
    return redirect('/');
})->name('logout');

    // Redirect old visitor routes to new ones
    Route::get('/visitor/register', function() {
        return redirect('/register', 301); // 301 = permanent redirect
    })->name('visitor.register.old');


// Your existing routes
Route::middleware('guest')->group(function () {
    Route::get('/register',            VisitorRegister::class)->name('visitor.register');
    Route::get('/login',               VisitorLogin::class)->name('visitor.login');
    Route::get('/auth/login-alias', function() {
        return redirect('/login');
    })->name('login');

    Route::get('/reset-password',      VisitorResetPassword::class)->name('visitor.reset-password');
    Route::get('/verify-new-password', VerifyNewPassword::class)->name('visitor.reset-password.verify');
    Route::get('/create-new-password', CreateNewPassword::class)->name('visitor.reset-password.create-password');
});

/*
|--------------------------------------------------------------------------
| Authenticated visitor area (uses Sanctum session guard)
|--------------------------------------------------------------------------
*/
Route::prefix('visitor')
    ->name('visitor.')
    ->middleware(['auth:sanctum', 'session.expired'])      // Added session expiration check
    ->group(function () {

        /*
        |---------------------------------------------------------------
        | E-mail verification
        |---------------------------------------------------------------
        */
        Route::get('email/verify', [EmailVerificationController::class, 'notice'])
            ->name('verification.notice');

        Route::get('email/verify/{id}/{hash}',
            [EmailVerificationController::class, 'verify'])
            ->middleware(['signed', 'throttle:6,1']) // signed URL + rate-limit
            ->name('verification.verify');

        Route::post('email/resend', [EmailVerificationController::class, 'resend'])
            ->middleware('throttle:6,1')
            ->name('verification.send');

        /*
        |---------------------------------------------------------------
        | Visitor ticket and name tag
        |---------------------------------------------------------------
        */
            Route::get('/ticket',                     PaymentProcess::class)->name('ticket');
            Route::get('/ticket/payment/success',     PaymentProcess::class)->name('ticket.payment.success');
            Route::get('/ticket/payment/cancel',      PaymentProcess::class)->name('ticket.payment.cancel');

        /*
        |---------------------------------------------------------------
        | LinkedIn OAuth callback
        |---------------------------------------------------------------
        | LinkedIn sends the browser back here.
        | We still want the user’s session, so keep auth:sanctum.
        | No CSRF needed because it is a GET request coming from LinkedIn.
        */
        Route::get('/linkedin/callback', ShareOnLinkedin::class)->name('linkedin.callback');
    });
