<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Future of Finance MEA | Cairo 2025</title>
    <!-- CSS files first -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/lipis/flag-icons@7.2.3/css/flag-icons.min.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-WNWNNHZD');</script>

    {{-- ONLY load the main app.css - it contains all your imported CSS files --}}
    @vite(['resources/css/app.css'])

    {{-- REMOVE these lines - they don't exist in your manifest --}}
    {{-- @vite(['resources/css/visitor-ticket.css']) --}}
    {{-- @vite(['resources/css/visitor-login.css']) --}}
    {{-- @vite('resources/css/visitor-reset-password.css') --}}
    {{-- @vite('resources/css/verify-new-password.css') --}}

    @livewireStyles

  <meta name="description" content="The Future of Finance MEA | Cairo 2025 conference is the perfect opportunity to stay ahead of the curve in the ever-changing world of finance. Join us on September 06, 2025, to learn from the best and connect with your peers.">
    <meta name="keywords" content="AFP,Futureoffinance,Future,Finance,Beacon,FinTrain,AFP_Future_of_Finance,MEA,Conference,Ritz,Nile,Carlton,Workshop,Kempiniski ,  ">
    <meta name="author" content="Future of Finance MEA | Cairo 2025">
    <meta property="og:title" content="Future of Finance MEA | Cairo 2025">
<meta property="og:url" content="https://cairo.beacon.com.eg/">
<meta property="og:image" content="https://cairo.beacon.com.eg/storage/logo/forumlogo.png">
    <link rel="icon" href="https://cairo.beacon.com.eg/storage/logo/eventicon.png">
</head>
{{--<body class="mainbody" style='background: url("{{ asset('images/event_background.png') }}"), radial-gradient(circle, rgba(255,255,255,1) 0%, rgba(255,255,255,0) 48%, rgba(255,255,255,1) 100%) no-repeat fixed;font-family: GothamNarrow, Montserrat, sans-serif;'>--}}

    <!-- Overlay for better content readability -->
    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;  z-index: -1;"></div>
    <div class="min-vh-100 d-flex flex-column" >
        <!-- Custom navigation bar -->
        <nav class="custom-navbar" style="display:none;">
            <div class="container">
                <a href="https://fintrain.beacon.com.eg/" class="custom-navbar-brand">
                    <img src="{{ asset('images/beacon_blue.png') }}" alt="Beacon FinTrain Logo">
                </a>

                <button class="custom-navbar-toggle" id="navbarToggle" aria-label="Toggle navigation">
                    <i class="bi bi-list"></i>
                </button>

                <ul class="custom-navbar-menu" id="navbarMenu">
                    <li><a href="https://fintrain.beacon.com.eg/aboutUs">About</a></li>
                    <li><a href="https://fintrain.beacon.com.eg/courses">Programs</a></li>
                    <li><a href="https://fintrain.beacon.com.eg/corporateTraining">Corporate Training</a></li>
                    <li class="dropdown">
                        <a href="#" class="dropbtn">More <i class="bi bi-chevron-down"></i></a>
                        <div class="dropdown-content">
                            <a href="#">Contact</a>
                        </div>
                    </li>
                    <li>
                        <a href="#" class="contact-us-btn">
                            <span>Contact</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Second Navbar with event title and timer -->
        <div class="bg-primary text-white py-2">
            <div class="container d-flex flex-column flex-md-row align-items-center justify-content-between">
                <div class="d-flex align-items-center mb-2 mb-md-0">
                    <a class="navbar-brand" href="/">
                    <img src="{{ asset('images/logo-event-white.png') }}" alt="AFP Logo">
                    </a>
                    <h1 class="fw-bold ms-3 fs-5">Future of Finance MEA | Cairo 2025</h1>
                </div>
                <div class="d-flex align-items-center gap-3">
                    <div class="text-center">
                        <span class="fs-4 fw-bold" id="days">00</span><br><span class="small">Days</span>
                    </div>
                    <div class="text-center">
                        <span class="fs-4 fw-bold" id="hours">00</span><br><span class="small">Hours</span>
                    </div>
                    <div class="text-center">
                        <span class="fs-4 fw-bold" id="minutes">00</span><br><span class="small">Minutes</span>
                    </div>
                    <div class="text-center">
                        <span class="fs-4 fw-bold" id="seconds">00</span><br><span class="small">Seconds</span>
                    </div>
                </div>
            </div>
        </div>

        <main class="flex-grow-1">
            {{ $slot }}
        </main>

        <!-- Footer -->
        <footer class="bg-primary text-white pt-5 pb-3">
            <div class="container">
                <div class="row align-items-center mb-4">
                    <div class="col-md-8 mb-3 mb-md-0">
                        <h5 class="fw-bold text-warning">STAY UPDATED</h5>
                        <p class="mb-0">Join our mailing list to receive exclusive offers and financial training resources.</p>
                    </div>
                    <div class="col-md-4">
                        <div class="bg-light rounded-3 overflow-hidden">
                            <form class="d-flex">
                                <!-- e-mail input -->
                                <input  type="email"
                                        class="form-control border-0 bg-transparent ps-4"
                                        placeholder="Enter email address"
                                        required>

                                <!-- button -->
                                <button type="submit"
                                        class="btn btn-warning fw-bold px-4">
                                    Subscribe
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="row mb-4">
                    <div class="col-md-3">
                        <ul class="list-unstyled small">
                            <li class="fw-bold mb-2">Beacon FinTrain</li>
                            <li><a href="https://fintrain.beacon.com.eg/aboutUs" class="text-white text-decoration-none">About us</a></li>
                            <li><a href="https://fintrain.beacon.com.eg/courses" class="text-white text-decoration-none">Courses</a></li>
                            <li><a href="https://fintrain.beacon.com.eg/corporateTraining" class="text-white text-decoration-none">Corporate Training</a></li>
                            <li><a href="https://fintrain.beacon.com.eg/contactUs" class="text-white text-decoration-none">Contact</a></li>
                            <li><a href="https://fintrain.beacon.com.eg/calendar" class="text-white text-decoration-none">Calendar</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3" style="display:none;">
                        <ul class="list-unstyled small">
                            <li class="fw-bold mb-2">AFP MEA</li>
                            <li><a href="https://fintrain.beacon.com.eg/afp" class="text-white text-decoration-none">AFP MEA</a></li>
                            <li><a href="https://fintrain.beacon.com.eg/courses/ctp-1" class="text-white text-decoration-none">CTP</a></li>
                            <li><a href="https://fintrain.beacon.com.eg/courses/fpac-2" class="text-white text-decoration-none">FPAC</a></li>
                            <li><a href="https://afpmembership.beacon.com.eg/" class="text-white text-decoration-none">In-Person RoundTables</a></li>
                            <li><a href="https://www.afponline.org/membership/explore-membership/join" class="text-white text-decoration-none">AFP Membership</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3">
                        <ul class="list-unstyled small">
                            <li class="fw-bold mb-2">More</li>
                            <li><a href="https://fintrain.beacon.com.eg/careers" class="text-white text-decoration-none">Careers</a></li>
                        </ul>
                    </div>
                    <div class="col-md-3 text-md-end">
                        <img src="{{ asset('images/beacon_white.png') }}" alt="Beacon FinTrain Logo">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <small>&copy; 2025 Beacon FinTrain</small>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <a href="https://www.facebook.com/Beaconfintrain/" class="text-white text-decoration-none ms-2"><i class="bi bi-facebook"></i></a>
                        <a href="https://www.youtube.com/channel/UC4y-QmmFYzgwc7aOj7RBI1A" class="text-white text-decoration-none ms-2"><i class="bi bi-youtube"></i></a>
                        <a href="https://www.instagram.com/beaconfintrain/#" class="text-white text-decoration-none ms-2"><i class="bi bi-instagram"></i></a>
                        <a href="https://www.linkedin.com/school/beaconfintrain" class="text-white text-decoration-none ms-2"><i class="bi bi-linkedin"></i></a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Scripts at the end of body -->
    @livewireScripts
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WNWNNHZD"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- SweetAlert for Session Messages and Livewire Events -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Success message from session
            @if(session()->has('success'))
                Swal.fire({
                    title: 'Success!',
                    text: '{{ session('success') }}',
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#3085d6'
                });
            @endif

            // Error message from session
            @if(session()->has('error'))
                Swal.fire({
                    title: 'Error!',
                    text: '{{ session('error') }}',
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#d33'
                });
            @endif

            // Generic message from session
            @if(session()->has('message'))
                Swal.fire({
                    title: 'Notification',
                    text: '{{ session('message') }}',
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#3085d6'
                });
            @endif

            // Livewire event listeners for SweetAlert
            window.addEventListener('swal:success', event => {
                console.log(event.detail);
                Swal.fire({
                    title: event.detail[0].title,
                    text: event.detail[0].text,
                    icon: 'success',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#3085d6'
                });
            });

            window.addEventListener('swal:error', event => {
                Swal.fire({
                    title: event.detail[0].title || 'Error!',
                    text: event.detail[0].text,
                    icon: 'error',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#d33'
                });
            });

            window.addEventListener('swal:message', event => {
                Swal.fire({
                    title: event.detail[0].title || 'Notification',
                    text: event.detail[0].text,
                    icon: 'info',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#3085d6'
                });
            });
        });
    </script>
    <script>
        // Custom navbar functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navbarToggle = document.getElementById('navbarToggle');
            const navbarMenu = document.getElementById('navbarMenu');
            const dropdowns = document.querySelectorAll('.dropdown');

            // Toggle mobile menu
            navbarToggle.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                navbarMenu.classList.toggle('active');
            });

            // Handle dropdowns on mobile
            dropdowns.forEach(dropdown => {
                const dropbtn = dropdown.querySelector('.dropbtn');

                // For mobile: toggle dropdown on click
                dropbtn.addEventListener('click', function(e) {
                    if (window.innerWidth <= 991) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Close other dropdowns
                        dropdowns.forEach(d => {
                            if (d !== dropdown) {
                                d.classList.remove('active');
                            }
                        });

                        dropdown.classList.toggle('active');
                    }
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!navbarToggle.contains(e.target) && !navbarMenu.contains(e.target)) {
                    navbarMenu.classList.remove('active');
                    dropdowns.forEach(dropdown => dropdown.classList.remove('active'));
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 991) {
                    navbarMenu.classList.remove('active');
                    dropdowns.forEach(dropdown => dropdown.classList.remove('active'));
                }
            });
        });
    </script>

    {{-- Load JavaScript files that exist in your manifest --}}
    @vite(['resources/js/app.js', 'resources/js/otp-input.js'])
      @vite(['resources/js/visitor-registration.js'])
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>


</html>
