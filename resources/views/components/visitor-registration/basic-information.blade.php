<div>
    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="firstName" class="form-label">First Name*</label>
            <input type="text" wire:model.live="firstName" id="firstName" class="form-control rounded-3 py-3" placeholder="Enter Your First Name">
            @error('firstName') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="lastName" class="form-label">Last Name*</label>
            <input type="text" wire:model.live="lastName" id="lastName" class="form-control rounded-3 py-3" placeholder="Enter Your Last Name">
            @error('lastName') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="nationality" class="form-label">Nationality*</label>
            <select wire:model.live="nationality" id="nationality" class="form-select rounded-3 py-3">
                <option value="">Select Your Nationality</option>
                @foreach($countries as $countryOption)
                    <option value="{{ $countryOption['nationality'] }}">
                        {{ $countryOption['nationality'] }}
                    </option>
                @endforeach
            </select>
            @error('nationality') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="country" class="form-label">Country*</label>
            <select wire:model.live="country" id="country" class="form-select rounded-3 py-3">
                <option value="">Select Your Country</option>
                @foreach($countries as $countryOption)
                    <option
                        value="{{ $countryOption['code'] }}"
                        data-flag="{{ strtolower($countryOption['code']) }}"
                        data-calling-code="{{ $countryOption['calling_code'] }}"
                    >
                        {{ $countryOption['name'] }}
                    </option>
                @endforeach
            </select>
            @error('country') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <!-- Phone field with proper flag display -->
    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="email" class="form-label">Email*</label>
            <input type="email" wire:model.live="email" id="email" class="form-control rounded-3 py-3" placeholder="Enter Your Email">
            @error('email') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="discountCode" class="form-label">Discount Code</label>
            <input type="text" wire:model.live="discountCode" id="discountCode" class="form-control rounded-3 py-3" placeholder="Enter Your Discount Code">
            @error('discountCode') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <!-- Discount code and Whatsapp number fields -->
    <div class="row mb-3">
        <div class="col-md-6">
            <label for="whatsappNumber" class="form-label">Whatsapp Number</label>
            <div class="input-group rounded-3">
                <div
                    x-data="countryDropdown()"
                    x-init="initCountryDropdown('{{ strtolower($whatsappCountryCode) }}', '{{ $this->getWhatsappCallingCode() }}', true)"
                    @whatsapp-country-updated.window="updateSelectedCountry($event.detail.countryCode, $event.detail.callingCode)"
                    class="country-dropdown-container whatsapp-country-dropdown"
                >
                    <button
                        type="button"
                        @click="toggleDropdown"
                        class="btn country-select-button"
                    >
                        <span :class="'fi fi-' + selectedCountryCode"></span>
                        <span x-text="'+' + selectedCallingCode"></span>
                        <span><i class="bi bi-chevron-down"></i></span>
                    </button>

                    <div
                        x-show="isOpen"
                        @click.away="isOpen = false"
                        class="country-dropdown"
                        x-transition
                    >
                        <div class="country-search-container">
                            <input
                                type="text"
                                x-model="searchQuery"
                                @input="filterCountries"
                                placeholder="Search countries..."
                                class="form-control form-control-sm"
                                x-ref="searchInput"
                            >
                        </div>

                        <div class="country-list">
                            @foreach($countries as $countryOption)
                                <div
                                    class="country-item"
                                    x-show="isVisible('{{ $countryOption['name'] }}', '{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                    @click="selectCountry('{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                >
                                        <span class="fi fi-{{ strtolower($countryOption['code']) }} me-2"></span>
                                    <span>{{ $countryOption['name'] }} (+{{ $countryOption['calling_code'] }})</span>
                                </div>
                            @endforeach
                        </div>
                    </div>

                    <input type="hidden" wire:model.live="whatsappCountryCode" id="whatsapp-country-hidden-input">
                </div>

                <input type="tel" wire:model.live="whatsappNumber" id="whatsappNumber" class="form-control py-3" placeholder="Enter Your Whatsapp">
            </div>
            @error('whatsappNumber') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
                <div class="col-md-6">
                    <label for="phone" class="form-label">Phone Number*</label>
                    <div class="input-group rounded-3">
                        <div
                            x-data="countryDropdown()"
                            x-init="initCountryDropdown('{{ strtolower($country) }}', '{{ $selectedCountryCallingCode }}')"
                            @country-updated.window="updateSelectedCountry($event.detail.countryCode, $event.detail.callingCode)"
                            class="country-dropdown-container"
                        >
                            <button
                                type="button"
                                @click="toggleDropdown"
                                class="btn country-select-button"
                            >
                                <span :class="'fi fi-' + selectedCountryCode"></span>
                                <span x-text="'+' + selectedCallingCode"></span>
                                <span><i class="bi bi-chevron-down"></i></span>
                            </button>

                            <div
                                x-show="isOpen"
                                @click.away="isOpen = false"
                                class="country-dropdown"
                                x-transition
                            >
                                <div class="country-search-container">
                                    <input
                                        type="text"
                                        x-model="searchQuery"
                                        @input="filterCountries"
                                        placeholder="Search countries..."
                                        class="form-control form-control-sm"
                                        x-ref="searchInput"
                                    >
                                </div>

                                <div class="country-list">
                                    @foreach($countries as $countryOption)
                                        <div
                                            class="country-item"
                                            x-show="isVisible('{{ $countryOption['name'] }}', '{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                            @click="selectCountry('{{ $countryOption['code'] }}', '{{ $countryOption['calling_code'] }}')"
                                        >
                                                <span class="fi fi-{{ strtolower($countryOption['code']) }} me-2"></span>
                                            <span>{{ $countryOption['name'] }} (+{{ $countryOption['calling_code'] }})</span>
                                        </div>
                                    @endforeach
                                </div>
                            </div>

                            <input type="hidden" wire:model.live="countryCode" id="country-hidden-input">
                        </div>

                        <input type="tel" wire:model.live="phone" id="phone" class="form-control py-3" placeholder="Enter Your Number">
                    </div>
                    @error('phone') <span class="text-danger">{{ $message }}</span> @enderror
                </div>
    </div>

    <!-- Password fields -->
    <div class="row mb-4">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="password" class="form-label">Password*</label>
            <div class="position-relative">
                <input
                    type="password"
                    wire:model.defer="password"
                    id="password"
                    class="form-control rounded-3 py-3"
                    placeholder="Create Your Password"
                >
                <button
                    type="button"
                    class="btn position-absolute top-50 end-0 translate-middle-y me-2"
                    data-toggle="password"
                >
                    <i class="bi bi-eye-slash"></i>
                </button>
            </div>
            @error('password') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="password_confirmation" class="form-label">Confirm Password*</label>
            <div class="position-relative">
                <input
                    type="password"
                    wire:model.defer="passwordConfirmation"
                    id="passwordConfirmation"
                    class="form-control rounded-3 py-3"
                    placeholder="Confirm Your Password"
                >
                <button
                    type="button"
                    class="btn position-absolute top-50 end-0 translate-middle-y me-2"
                    data-toggle="passwordConfirmation"
                >
                    <i class="bi bi-eye-slash"></i>
                </button>
            </div>
            @error('passwordConfirmation') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <div class="mb-4">
        <label for="profilePhoto" class="form-label d-flex align-items-center">
            Upload your professional photo
            <span class="ms-2" data-bs-toggle="tooltip" data-bs-placement="top" title="Please upload a professional headshot photo">
                <i class="bi bi-info-circle text-primary"></i>
            </span>
        </label>
        <div class="d-flex flex-column flex-md-row align-items-center">
            <div class="me-md-4 mb-3 mb-md-0 flex-grow-1">
                <input
                    type="file"
                    id="profilePhotoInput"
                    class="form-control"
                    accept="image/*"
                >
                <div class="mt-2 text-danger">
                    @error('profilePhoto') {{ $message }} @enderror
                </div>
                <div class="text-muted small mt-1">The image must not exceed 1MB</div>
            </div>
            <div class="profile-preview-container" id="profilePreviewContainer">
                @if ($profilePhoto)
                    <img src="{{ $profilePhoto->temporaryUrl() }}" alt="Profile Preview" class="img-thumbnail profile-preview" id="profilePreviewImage">
                    <button type="button" class="btn btn-sm btn-danger edit-photo-btn" onclick="removeProfilePhoto()">
                        <i class="bi bi-trash"></i>
                    </button>
                @else
                    <div class="profile-placeholder d-flex align-items-center justify-content-center" id="profilePlaceholder">
                        <i class="bi bi-person-fill fs-1 text-muted"></i>
                    </div>
                @endif
            </div>
        </div>
        @if(isset($showWorkshopSelection) && $showWorkshopSelection)
        <!-- Workshop selection shown only when corporate code present -->
            <div class="row mb-3">
                <div class="col-12">
                    <label for="selectedWorkshop" class="form-label">Select Workshop*</label>
                    <select id="selectedWorkshop" wire:model.live="selectedWorkshop" class="form-select rounded-3 py-3">
                        <option value="" selected disabled hidden>Select Workshop (Optional)</option>

                        @php
                            $morningWorkshops = $workshops->where('time', 'Morning');
                            $eveningWorkshops = $workshops->where('time', 'Evening');
                            $otherWorkshops = $workshops->whereNotIn('time', ['Morning', 'Evening'])->where('time', '!=', null);
                            $noTimeWorkshops = $workshops->whereNull('time');
                            $allOtherWorkshops = $otherWorkshops->merge($noTimeWorkshops);
                        @endphp

                        @if($morningWorkshops && $morningWorkshops->count() > 0)
                            <optgroup label="Morning Workshops">
                                @foreach($morningWorkshops as $workshop)
                                    <option style="font-weight: 100;"
                                            {{ $workshop->max_attendees > 0 ? '' : 'disabled' }}
                                            value="{{ $workshop->id }}">
                                        {{ $workshop->name }}
                                        {{ $workshop->max_attendees > 0 ? '' : ' "Fully Booked"' }}
                                        @if($workshop->start_from)
                                            - {{ $workshop->start_from }}
                                        @endif
                                    </option>
                                @endforeach
                            </optgroup>
                        @endif

                        @if($eveningWorkshops && $eveningWorkshops->count() > 0)
                            <optgroup label="Evening Workshops">
                                @foreach($eveningWorkshops as $workshop)
                                    <option style="font-weight: 100;"
                                            {{ $workshop->max_attendees > 0 ? '' : 'disabled' }}
                                            value="{{ $workshop->id }}">
                                        {{ $workshop->name }}
                                        {{ $workshop->max_attendees > 0 ? '' : ' "Fully Booked"' }}
                                        @if($workshop->start_from)
                                            - {{ $workshop->start_from }}
                                        @endif
                                    </option>
                                @endforeach
                            </optgroup>
                        @endif

                        @if($allOtherWorkshops && $allOtherWorkshops->count() > 0)
                            <optgroup label="Other Workshops">
                                @foreach($allOtherWorkshops as $workshop)
                                    <option style="font-weight: 100;"
                                            {{ $workshop->max_attendees > 0 ? '' : 'disabled' }}
                                            value="{{ $workshop->id }}">
                                        {{ $workshop->name }}
                                        {{ $workshop->max_attendees > 0 ? '' : ' "Fully Booked"' }}
                                        @if($workshop->start_from)
                                            - {{ $workshop->start_from }}
                                        @endif
                                    </option>
                                @endforeach
                            </optgroup>
                        @endif
                    </select>
                    @error('selectedWorkshop') <span class="text-danger">{{ $message }}</span> @enderror

                    {{-- Workshop Information Display --}}
                    @if($selectedWorkshop)
                        @php
                            $selectedWorkshopDetails = $workshops->find($selectedWorkshop);
                        @endphp
                        @if($selectedWorkshopDetails)
                            <div class="workshop-info-card mt-3 p-3 bg-light rounded-3 border">
                                <div class="d-flex align-items-start">
                                    <div class="me-3">
                                        <i class="bi bi-calendar-event text-primary fs-4"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1 text-primary">{{ $selectedWorkshopDetails->name }}</h6>
                                        <div class="small text-muted">
                                            @if($selectedWorkshopDetails->time)
                                                <div><strong>Time Slot:</strong> {{ $selectedWorkshopDetails->time }}</div>
                                            @endif
                                            @if($selectedWorkshopDetails->start_from)
                                                <div><strong>Starts at:</strong> {{ $selectedWorkshopDetails->start_from }}</div>
                                            @endif
                                            @if($selectedWorkshopDetails->duration)
                                                <div><strong>Duration:</strong> {{ $selectedWorkshopDetails->duration }} minutes</div>
                                            @endif
                                            @if($selectedWorkshopDetails->description)
                                                <div class="mt-2"><strong>Description:</strong> {{ $selectedWorkshopDetails->description }}</div>
                                            @endif
                                            <div class="mt-2">
                                                @if($selectedWorkshopDetails->max_attendees > 0)
                                                    <span class="badge bg-success">Available</span>
                                                @else
                                                    <span class="badge bg-danger">Fully Booked</span>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
            </div>

        @endif

        <div class="form-check mt-3">
            <input class="form-check-input" type="checkbox" wire:model.live="photoConsent" id="photoConsent">
            <label class="form-check-label" for="photoConsent">
                By submitting your photo, you grant us the right to use them for marketing purposes, without sharing your personal information with any third party. Selfies, personal pictures, and pixelated pictures are not permitted at this event.
            </label>
        </div>
        @error('photoConsent') <span class="text-danger">{{ $message }}</span> @enderror
    </div>
</div>

<!-- Image Cropper Modal with improved responsive design -->
<div class="modal fade" id="imageCropperModal" tabindex="-1" aria-labelledby="imageCropperModalLabel" role="dialog" aria-modal="true" data-bs-backdrop="true" data-bs-keyboard="true">
    <div class="modal-dialog modal-xl image-cropper-modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageCropperModalLabel">Crop Your Profile Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body image-cropper-modal-body">
                <div class="text-center mb-3">
                    <small class="text-muted">Drag to move, scroll to zoom, and resize the crop area as needed.</small>
                </div>
                <div class="img-container">
                    <img id="imageToCrop" src="" alt="Image to crop" class="cropper-image">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="cropButton">
                    <i class="bi bi-crop"></i> Crop & Save
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Workshop Selection Styling */
    .form-select optgroup {
        font-weight: bold;
        color: #495057;
        background-color: #f8f9fa;
        padding: 8px;
        font-size: 14px;
    }

    .form-select option {
        font-weight: normal;
        padding: 8px 12px;
        color: #212529;
    }

    .form-select option:disabled {
        color: #6c757d;
        background-color: #f8f9fa;
        font-style: italic;
    }

    .workshop-info-card {
        border: 1px solid #dee2e6 !important;
        background-color: #f8f9fa !important;
        transition: all 0.3s ease;
    }

    .workshop-info-card:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .workshop-info-card .bi-calendar-event {
        color: #0d6efd !important;
    }

    .workshop-info-card h6 {
        color: #0d6efd !important;
        font-weight: 600;
    }

    .workshop-info-card .badge {
        font-size: 0.75rem;
        padding: 0.375rem 0.75rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .form-select {
            font-size: 16px; /* Prevent zoom on iOS */
        }

        .workshop-info-card {
            padding: 0.75rem !important;
        }

        .workshop-info-card .small {
            font-size: 0.8rem;
        }
    }

    /* Enhanced optgroup styling */
    .form-select optgroup[label="Morning Workshops"] {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        color: #856404;
    }

    .form-select optgroup[label="Evening Workshops"] {
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
        color: #0c5460;
    }

    .form-select optgroup[label="Other Workshops"] {
        background: linear-gradient(135deg, #e2e3e5 0%, #d6d8db 100%);
        color: #383d41;
    }
</style>
