<div>
    <div class="text-center mb-4">
        <h3 class="text-primary fw-bold mb-3">Almost there!</h3>
        <p class="text-muted">Please review your information before submitting</p>
    </div>

    <div class="card border-0 shadow-sm rounded-4 overflow-hidden mb-4">
        <!-- Profile Header -->
        <div class="bg-primary bg-gradient text-white p-4 text-center">
            @if($profilePhoto)
                <div class="mx-auto mb-3" style="width: 100px; height: 100px; border-radius: 50%; overflow: hidden; border: 3px solid white;">
                    <img src="{{ $profilePhoto->temporaryUrl() }}" alt="Profile Photo" class="w-100 h-100" style="object-fit: cover;">
                </div>
            @else
                <div class="mx-auto mb-3 d-flex align-items-center justify-content-center bg-white text-primary rounded-circle" style="width: 100px; height: 100px; border: 3px solid white;">
                    <i class="bi bi-person fs-1"></i>
                </div>
            @endif
            <h4 class="mb-1 fw-bold">{{ $firstName }} {{ $lastName }}</h4>
            <p class="mb-0 opacity-90">{{ $jobTitle }}</p>
            <p class="mb-0 opacity-90 small">{{ $companyName }}</p>
        </div>

        <!-- Personal Information -->
        <div class="p-4 border-bottom">
            <h5 class="fw-bold mb-3 text-primary">
                <i class="bi bi-person-badge me-2"></i>Personal Information
            </h5>
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-envelope"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Email</div>
                            <div>{{ $email }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-telephone"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Phone</div>
                            <div>+{{ $selectedCountryCallingCode }} {{ $phone }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-globe"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Nationality</div>
                            <div>{{ ucfirst($nationality) }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Company Information -->
        <div class="p-4">
            <h5 class="fw-bold mb-3 ">
                <i class="bi bi-bank me-2"></i>Company Information
            </h5>
            <div class="row g-3">
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-bank"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Company</div>
                            <div>{{ $companyName }}</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-briefcase"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Job Title</div>
                            <div>{{ $jobTitle }}</div>
                        </div>
                    </div>
                </div>
                @if(!empty($jobTitle2))
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-briefcase"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Secondary Job Title</div>
                            <div>{{ $jobTitle2 }}</div>
                        </div>
                    </div>
                </div>
                @endif
                <div class="col-md-6">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-diagram-3"></i>
                        </div>
                        <div>
                            <div class="text-muted small">Industry</div>
                            <div>{{ ucfirst($industry) }}</div>
                        </div>
                    </div>
                </div>
                @if($linkedin)
                <div class="col-12">
                    <div class="d-flex align-items-center mb-2">
                        <div class="me-3 text-primary" style="width: 24px; text-align: center;">
                            <i class="bi bi-linkedin"></i>
                        </div>
                        <div>
                            <div class="text-muted small">LinkedIn</div>
                            <div><a href="{{ $linkedin }}" target="_blank" class="text-decoration-none text-break">{{ $linkedin }}</a></div>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <div class="d-flex gap-3 mt-4">
        <button type="button" wire:click="previousStep" class="btn btn-outline-primary py-3 px-4 rounded-3 flex-grow-1">
            <i class="bi bi-arrow-left me-2"></i> Back
        </button>
        <button type="button" wire:click="register" class="btn btn-primary py-3 px-5 rounded-3 flex-grow-1">
            Complete Registration <i class="bi bi-check-circle ms-2"></i>
        </button>
    </div>
</div>
