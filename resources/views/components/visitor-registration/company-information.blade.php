<div>
    <div class="text-center mb-4">
        <h4 class="text-primary">Company Information</h4>
        <p class="text-muted">Tell us about your company</p>
    </div>

    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="companyName" class="form-label">Company Name*</label>
            <input type="text" wire:model.lazy="companyName" id="companyName" class="form-control rounded-3 py-3" placeholder="Enter Your Company Name">
            @error('companyName') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="industry" class="form-label">Company Sector*</label>
            <select wire:model.lazy="industry" id="industry" class="form-select rounded-3 py-3">
                <option value="">Select Your Company Sector</option>
                @if(isset($sectors))
                    @foreach($sectors as $sector)
                        <option value="{{ $sector['name'] }}">{{ $sector['name'] }}</option>
                    @endforeach
                @endif
            </select>
            @error('industry') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6 mb-3 mb-md-0">
            <label for="jobTitle" class="form-label">Job Title 1*</label>
            <input type="text" wire:model.lazy="jobTitle" id="jobTitle" class="form-control rounded-3 py-3" placeholder="Enter Your Job Title">
            @error('jobTitle') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
        <div class="col-md-6">
            <label for="jobTitle2" class="form-label">Job Title 2</label>
            <input type="text" wire:model.lazy="jobTitle2" id="jobTitle2" class="form-control rounded-3 py-3" placeholder="Enter Your Job Title">
            @error('jobTitle2') <span class="text-danger">{{ $message }}</span> @enderror
        </div>
    </div>

    <div class="mb-4">
        <label for="linkedin" class="form-label">Your LinkedIn Profile</label>
        <div class="input-group">
            <input type="text" wire:model.lazy="linkedin" id="linkedin" class="form-control rounded-3 py-3" placeholder="Enter Your LinkedIn Profile URL">
        </div>
        @error('linkedin') <span class="text-danger">{{ $message }}</span> @enderror
    </div>
</div>
