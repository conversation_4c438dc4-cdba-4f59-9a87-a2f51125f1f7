<div class="progress-container mb-4">
    <div class="d-flex justify-content-between align-items-center px-4 py-3 bg-white rounded">
        @foreach([
            ['step' => 1, 'label' => 'Basic Information'],
            ['step' => 2, 'label' => 'Company Details'],
            ['step' => 3, 'label' => 'Finish']
        ] as $index => $item)
            <div class="text-center">
                <div class="progress-step {{ $currentStep > $item['step'] ? 'completed' : ($currentStep == $item['step'] ? 'current' : '') }}">
                    <span class="progress-circle">
                        {{ $currentStep > $item['step'] ? '✓' : $item['step'] }}
                    </span>
                    <span class="progress-text">{{ $item['label'] }}</span>
                </div>
            </div>
            @if(!$loop->last)
                <div class="progress-line {{ $currentStep > ($index + 1) ? 'completed' : ($currentStep == ($index + 2) ? 'current-before' : '') }}"></div>
            @endif
        @endforeach
    </div>
</div>
