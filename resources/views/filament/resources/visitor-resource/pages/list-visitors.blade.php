<x-filament-panels::page>
    @if($activeTab === 'qr_attendance')
        <div class="space-y-6">
            <!-- Statistics Section -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                @php
                    $totalVisitors = \App\Models\Visitor::count();
                    $attendedVisitors = \App\Models\Visitor::where('attend', true)->count();
                    $visitorsWithQr = \App\Models\Visitor::whereNotNull('qr_code')->count();
                    $attendanceRate = $totalVisitors > 0 ? round(($attendedVisitors / $totalVisitors) * 100, 1) : 0;
                @endphp

                <div class="bg-white rounded-lg shadow p-4">
                    <div class="text-2xl font-bold text-blue-600">{{ $totalVisitors }}</div>
                    <div class="text-sm text-gray-600">Total Visitors</div>
                </div>

                <div class="bg-white rounded-lg shadow p-4">
                    <div class="text-2xl font-bold text-green-600">{{ $attendedVisitors }}</div>
                    <div class="text-sm text-gray-600">Attended</div>
                </div>

                <div class="bg-white rounded-lg shadow p-4">
                    <div class="text-2xl font-bold text-purple-600">{{ $visitorsWithQr }}</div>
                    <div class="text-sm text-gray-600">With QR Codes</div>
                </div>

                <div class="bg-white rounded-lg shadow p-4">
                    <div class="text-2xl font-bold text-orange-600">{{ $attendanceRate }}%</div>
                    <div class="text-sm text-gray-600">Attendance Rate</div>
                </div>
            </div>

            <!-- QR Code Lookup Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">QR Code Lookup</h3>
                    <p class="text-sm text-gray-600">Enter a QR code to mark visitor attendance</p>

                    <div class="flex gap-4">
                        <div class="flex-1">
                            <label for="qrCode" class="block text-sm font-medium text-gray-700 mb-1">Enter QR Code</label>
                            <input
                                type="text"
                                id="qrCode"
                                wire:model.live="qrCode"
                                placeholder="Scan or enter QR code here"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                                autofocus
                            />
                        </div>
                        <div class="flex items-end">
                            <button
                                type="button"
                                wire:click="markAttendanceByQrCode"
                                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Mark Attendance
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visitor Search Section -->
            <div class="bg-white rounded-lg shadow p-6">
                <div class="space-y-4">
                    <h3 class="text-lg font-medium text-gray-900">Visitor Search</h3>
                    <p class="text-sm text-gray-600">Search for visitors by multiple criteria</p>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="searchName" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                            <input
                                type="text"
                                id="searchName"
                                wire:model.live="searchName"
                                placeholder="First, second, or family name"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                        <div>
                            <label for="searchEmail" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                            <input
                                type="email"
                                id="searchEmail"
                                wire:model.live="searchEmail"
                                placeholder="<EMAIL>"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                        <div>
                            <label for="searchCompany" class="block text-sm font-medium text-gray-700 mb-1">Company</label>
                            <input
                                type="text"
                                id="searchCompany"
                                wire:model.live="searchCompany"
                                placeholder="Company name"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                        <div>
                            <label for="searchPhone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                            <input
                                type="text"
                                id="searchPhone"
                                wire:model.live="searchPhone"
                                placeholder="Phone or WhatsApp number"
                                class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
                            />
                        </div>
                    </div>

                    <div class="flex gap-4">
                        <button
                            type="button"
                            wire:click="performSearch"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Search Visitors
                        </button>
                        <button
                            type="button"
                            wire:click="clearSearch"
                            class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Clear Search
                        </button>
                    </div>
                </div>
            </div>

            <!-- Visitors Table -->
            <div class="bg-white rounded-lg shadow">
                {{ $this->table }}
            </div>
        </div>

        @push('scripts')
        <script>
            // Auto-focus on QR code input when page loads
            document.addEventListener('DOMContentLoaded', function() {
                const qrCodeInput = document.querySelector('#qrCode');
                if (qrCodeInput) {
                    qrCodeInput.focus();
                }
            });

            // Listen for QR code scanner input (typically ends with Enter)
            document.addEventListener('keydown', function(event) {
                const qrCodeInput = document.querySelector('#qrCode');
                if (qrCodeInput && document.activeElement === qrCodeInput && event.key === 'Enter') {
                    event.preventDefault();
                    // Trigger the mark attendance action
                    @this.call('markAttendanceByQrCode');
                }
            });

            // Listen for tab changes and force refresh
            document.addEventListener('livewire:init', () => {
                Livewire.on('tab-changed', (event) => {
                    // Force a complete component refresh when tab changes
                    setTimeout(() => {
                        @this.$refresh();
                    }, 100);
                });
            });

            // Force refresh when tab buttons are clicked
            document.addEventListener('click', function(event) {
                if (event.target.closest('[role="tab"]')) {
                    setTimeout(() => {
                        @this.$refresh();
                    }, 200);
                }
            });
        </script>
        @endpush
    @else
        {{ $this->table }}
    @endif
</x-filament-panels::page>
