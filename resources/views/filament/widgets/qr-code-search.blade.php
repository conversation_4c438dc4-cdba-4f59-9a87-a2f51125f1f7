<div class="fi-wi-stats-overview-stat relative rounded-xl bg-white p-6 shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10">
    <div class="flex items-center gap-4">
        <!-- QR Code Icon -->
        <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-primary-50 dark:bg-primary-400/10">
            <svg class="h-6 w-6 text-primary-600 dark:text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h2M4 4h4m0 0v4m0 0H4m15.99.01L20 8.01"></path>
            </svg>
        </div>

        <!-- Search Input -->
        <div class="flex-1">
            <label for="qr-search" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                QR Code Attendance
            </label>
            <div class="relative">
                <input
                    type="text"
                    id="qr-search"
                    wire:model.live.debounce.500ms="qrCode"
                    placeholder="Scan or enter QR code here..."
                    class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-primary-500 dark:focus:ring-primary-500 sm:text-sm"
                    autocomplete="off"
                    autofocus
                />
                @if($qrCode)
                    <button
                        type="button"
                        wire:click="$set('qrCode', '')"
                        class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                    >
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                @endif
            </div>
            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Enter or scan a QR code to quickly find and mark visitor attendance
            </p>
        </div>

        <!-- Status Indicator -->
        <div class="flex items-center">
            @if($qrCode && strlen($qrCode) > 3)
                <div class="flex items-center text-sm text-blue-600 dark:text-blue-400">
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Searching...
                </div>
            @else
                <div class="text-sm text-gray-500 dark:text-gray-400">
                    Ready to scan
                </div>
            @endif
        </div>
    </div>

    <!-- Visitor Details Modal -->
    @if($showVisitorModal && $selectedVisitor)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" wire:click="closeModal"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6 dark:bg-gray-800">
                    <!-- Header -->
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white" id="modal-title">
                            Visitor Details
                        </h3>
                        <button type="button" wire:click="closeModal" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Visitor Information -->
                    <div class="mb-6">
                        @php
                            $imageUrl = $selectedVisitor->image
                                ? \App\Models\Visitor::formatImagePath($selectedVisitor->image, 'storage/visitor_images')
                                : asset('images/placeholder-nametag.png');

                            $attendanceStatus = $selectedVisitor->attend
                                ? 'Already marked as attended'
                                : 'Ready to mark attendance';

                            $bgColor = $selectedVisitor->attend
                                ? 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-800'
                                : 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';

                            $statusColor = $selectedVisitor->attend
                                ? 'text-orange-700 dark:text-orange-300'
                                : 'text-green-700 dark:text-green-300';

                            $statusIcon = $selectedVisitor->attend ? '⚠️' : '✅';
                        @endphp

                        <div class="p-4 {{ $bgColor }} border rounded-lg">
                            <!-- Status Header -->
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-md font-semibold text-gray-900 dark:text-white">Visitor Found</h4>
                                <span class="{{ $statusColor }} font-semibold text-sm px-3 py-1 rounded-full bg-white dark:bg-gray-700 border">
                                    {{ $statusIcon }} {{ $attendanceStatus }}
                                </span>
                            </div>

                            <!-- Visitor Details -->
                            <div class="flex items-start space-x-4">
                                <!-- Photo -->
                                <div class="flex-shrink-0">
                                    <img src="{{ $imageUrl }}" alt="Visitor Photo"
                                         class="w-16 h-16 rounded-full object-cover border-4 border-white shadow-lg">
                                </div>

                                <!-- Info -->
                                <div class="flex-1 min-w-0">
                                    <!-- ID Badge -->
                                    <div class="inline-flex items-center px-2 py-1 rounded-md bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs font-medium mb-2">
                                        ID: {{ $selectedVisitor->id }}
                                    </div>

                                    <!-- Name -->
                                    <h5 class="text-lg font-bold text-gray-900 dark:text-white mb-1">
                                        {{ trim($selectedVisitor->first_name . ' ' . $selectedVisitor->second_name) }}
                                    </h5>

                                    <!-- Contact Info -->
                                    <div class="space-y-1">
                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                                            </svg>
                                            {{ $selectedVisitor->email }}
                                        </div>

                                        @if($selectedVisitor->company)
                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                            {{ $selectedVisitor->company }}
                                        </div>
                                        @endif

                                        @if($selectedVisitor->title1)
                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                            </svg>
                                            {{ $selectedVisitor->title1 }}
                                        </div>
                                        @endif

                                        @if($selectedVisitor->phone)
                                        <div class="flex items-center text-sm text-gray-600 dark:text-gray-300">
                                            <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                            </svg>
                                            {{ $selectedVisitor->phone }}
                                        </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex justify-end space-x-3">
                        <button type="button" wire:click="closeModal"
                                class="inline-flex justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600">
                            Close
                        </button>

                        @if($selectedVisitor->attend)
                            <button type="button" wire:click="unmarkAttendance"
                                    class="inline-flex justify-center px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                                Unmark Attendance
                            </button>
                        @else
                            <div class="inline-flex items-center px-4 py-2 text-sm font-medium text-green-700 bg-green-100 border border-green-300 rounded-md dark:bg-green-900 dark:text-green-300 dark:border-green-700">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Automatically Marked
                            </div>
                        @endif
                    </div>

                    <!-- Auto-close countdown -->
                    <div class="mt-3 text-center">
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                            This window will close automatically in <span id="countdown">3</span> seconds...
                        </p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Auto-close JavaScript -->
    <script>
        document.addEventListener('livewire:init', () => {
            Livewire.on('auto-close-modal', (event) => {
                const delay = event.delay || 3000;
                const countdownElement = document.getElementById('countdown');
                let timeLeft = Math.ceil(delay / 1000);

                if (countdownElement) {
                    countdownElement.textContent = timeLeft;

                    const countdownInterval = setInterval(() => {
                        timeLeft--;
                        if (countdownElement) {
                            countdownElement.textContent = timeLeft;
                        }

                        if (timeLeft <= 0) {
                            clearInterval(countdownInterval);
                        }
                    }, 1000);
                }

                setTimeout(() => {
                    @this.closeModal();
                }, delay);
            });

            Livewire.on('focus-search-input', () => {
                setTimeout(() => {
                    const searchInput = document.getElementById('qr-search');
                    if (searchInput) {
                        searchInput.focus();
                        searchInput.select();
                    }
                }, 100);
            });
        });
    </script>
</div>
