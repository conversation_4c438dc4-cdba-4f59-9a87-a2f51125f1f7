@if($visitorFound === false)
    <div class="flex items-center justify-center p-6 bg-red-50 border border-red-200 rounded-lg">
        <div class="text-center">
            <div class="text-4xl mb-2">❌</div>
            <div class="text-red-700 font-semibold text-lg">No visitor found</div>
            <div class="text-red-600 text-sm">Please check the QR code and try again</div>
        </div>
    </div>
@elseif(!$visitorInfo)
    <div class="flex items-center justify-center p-6 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="text-center">
            <div class="text-4xl mb-2">📱</div>
            <div class="text-blue-700 font-semibold text-lg">Ready to scan</div>
            <div class="text-blue-600 text-sm">Enter a QR code to see visitor information</div>
        </div>
    </div>
@else
    @php
        $imageUrl = $visitorInfo['image']
            ? \App\Models\Visitor::formatImagePath($visitorInfo['image'], 'storage/visitor_images')
            : asset('images/placeholder-nametag.png');

        $attendanceStatus = $visitorInfo['already_attended']
            ? 'Already marked as attended'
            : 'Ready to mark attendance';

        $bgColor = $visitorInfo['already_attended']
            ? 'bg-orange-50 border-orange-200'
            : 'bg-green-50 border-green-200';

        $statusColor = $visitorInfo['already_attended']
            ? 'text-orange-700'
            : 'text-green-700';

        $statusIcon = $visitorInfo['already_attended']
            ? '⚠️'
            : '✅';
    @endphp

    <div class="p-6 {{ $bgColor }} border rounded-lg">
        <!-- Header with status -->
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-bold text-gray-900">Visitor Found</h3>
            <span class="{{ $statusColor }} font-semibold text-sm px-3 py-1 rounded-full bg-white border">
                {{ $statusIcon }} {{ $attendanceStatus }}
            </span>
        </div>

        <!-- Main visitor info -->
        <div class="flex items-start space-x-4">
            <!-- Visitor Photo -->
            <div class="flex-shrink-0">
                <img src="{{ $imageUrl }}" alt="Visitor Photo"
                     class="w-20 h-20 rounded-full object-cover border-4 border-white shadow-lg">
            </div>

            <!-- Visitor Details -->
            <div class="flex-1 min-w-0">
                <!-- ID Badge -->
                <div class="inline-flex items-center px-2 py-1 rounded-md bg-blue-100 text-blue-800 text-xs font-medium mb-2">
                    ID: {{ $visitorInfo['id'] }}
                </div>

                <!-- Name -->
                <h4 class="text-xl font-bold text-gray-900 mb-1">{{ $visitorInfo['name'] }}</h4>

                <!-- Contact Info -->
                <div class="space-y-1">
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
                        </svg>
                        {{ $visitorInfo['email'] }}
                    </div>

                    @if($visitorInfo['company'])
                    <div class="flex items-center text-sm text-gray-600">
                        <svg class="w-4 h-4 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        {{ $visitorInfo['company'] }}
                    </div>
                    @else
                    <div class="flex items-center text-sm text-gray-400">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                        No company specified
                    </div>
                    @endif
                </div>
            </div>
        </div>

        @if(!$visitorInfo['already_attended'])
        <!-- Action prompt -->
        <div class="mt-4 p-3 bg-white rounded-md border border-green-200">
            <div class="flex items-center">
                <div class="text-green-500 mr-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <span class="text-green-700 font-medium text-sm">Click "Submit" to mark this visitor as attended</span>
            </div>
        </div>
        @endif
    </div>
@endif
