<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Statistics Section -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            @php
                $totalWithNameTags = \App\Models\Visitor::whereNotNull('name_tag')->where('attend', true)->count();
                $receivedNameTags = \App\Models\Visitor::whereNotNull('name_tag')->where('attend', true)->where('receive_nameTag', true)->count();
                $pendingNameTags = $totalWithNameTags - $receivedNameTags;
                $distributionRate = $totalWithNameTags > 0 ? round(($receivedNameTags / $totalWithNameTags) * 100, 1) : 0;
            @endphp

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $totalWithNameTags }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Total Name Tags</div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $receivedNameTags }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Received</div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $pendingNameTags }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Pending</div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
                <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $distributionRate }}%</div>
                <div class="text-sm text-gray-600 dark:text-gray-400">Distribution Rate</div>
            </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Quick Actions</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="text-3xl mb-2">📋</div>
                    <div class="font-medium text-gray-900 dark:text-white">Pending Distribution</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $pendingNameTags }} name tags waiting</div>
                </div>
                
                <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="text-3xl mb-2">✅</div>
                    <div class="font-medium text-gray-900 dark:text-white">Completed</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $receivedNameTags }} name tags distributed</div>
                </div>
                
                <div class="text-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div class="text-3xl mb-2">📊</div>
                    <div class="font-medium text-gray-900 dark:text-white">Progress</div>
                    <div class="text-sm text-gray-600 dark:text-gray-400">{{ $distributionRate }}% completion rate</div>
                </div>
            </div>
        </div>

        <!-- Search and Filter Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Search & Filter</h3>
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="search-name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Search by Name
                    </label>
                    <input 
                        type="text" 
                        id="search-name"
                        wire:model.live.debounce.500ms="searchName"
                        placeholder="Enter visitor name..."
                        class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                </div>

                <div>
                    <label for="search-email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Search by Email
                    </label>
                    <input 
                        type="email" 
                        id="search-email"
                        wire:model.live.debounce.500ms="searchEmail"
                        placeholder="Enter email address..."
                        class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                </div>

                <div>
                    <label for="search-company" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Search by Company
                    </label>
                    <input 
                        type="text" 
                        id="search-company"
                        wire:model.live.debounce.500ms="searchCompany"
                        placeholder="Enter company name..."
                        class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                </div>

                <div>
                    <label for="filter-received" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Filter by Status
                    </label>
                    <select 
                        id="filter-received"
                        wire:model.live="filterReceived"
                        class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    >
                        <option value="all">All Visitors</option>
                        <option value="not_received">Not Received</option>
                        <option value="received">Received</option>
                    </select>
                </div>
            </div>

            <div class="mt-4 flex justify-end space-x-2">
                <button 
                    type="button"
                    wire:click="$set('searchName', '')"
                    wire:click="$set('searchEmail', '')"
                    wire:click="$set('searchCompany', '')"
                    wire:click="$set('filterReceived', 'all')"
                    class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                    Clear Filters
                </button>
            </div>
        </div>

        <!-- Instructions Section -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        Name Tag Distribution Instructions
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>This page shows all attended visitors who have generated name tags</li>
                            <li>Click the "Mark Received" button when a visitor picks up their name tag</li>
                            <li>Use the search and filter options to quickly find specific visitors</li>
                            <li>The "Received" column can be clicked to toggle the status quickly</li>
                            <li>Use bulk actions to mark multiple visitors at once</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Table -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            {{ $this->table }}
        </div>
    </div>
</x-filament-panels::page>
