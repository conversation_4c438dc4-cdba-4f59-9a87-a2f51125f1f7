<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Search Section -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <input
                        type="text"
                        wire:model.live.debounce.500ms="searchName"
                        placeholder="Search by visitor name..."
                        class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    />
                </div>

                <div>
                    <select
                        wire:model.live="filterReceived"
                        class="block w-full rounded-lg border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                    >
                        <option value="all">All Visitors</option>
                        <option value="not_received">Not Received</option>
                        <option value="received">Received</option>
                    </select>
                </div>

                <div>
                    <button
                        type="button"
                        wire:click="$set('searchName', '')"
                        wire:click="$set('filterReceived', 'all')"
                        class="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <!-- Visitor Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            @foreach($this->getVisitors() as $visitor)
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden border {{ $visitor->receive_nameTag ? 'border-green-200 dark:border-green-800' : 'border-gray-200 dark:border-gray-700' }}">
                    <!-- Visitor Image -->
                    <div class="aspect-w-16 aspect-h-9 bg-gray-100 dark:bg-gray-700">
                        @php
                            $imageUrl = $visitor->image
                                ? \App\Models\Visitor::formatImagePath($visitor->image, 'storage/visitor_images')
                                : asset('images/placeholder-nametag.png');
                        @endphp
                        <img src="{{ $imageUrl }}" alt="Visitor Photo" class="w-full h-48 object-cover">
                    </div>

                    <!-- Card Content -->
                    <div class="p-4">
                        <!-- Visitor ID -->
                        <div class="flex items-center justify-between mb-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                ID: {{ $visitor->letter }} {{ $visitor->number }}
                            </span>
                            @if($visitor->receive_nameTag)
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                    ✅ Received
                                </span>
                            @else
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                    📋 Pending
                                </span>
                            @endif
                        </div>

                        <!-- Visitor Name -->
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                            {{ trim($visitor->first_name . ' ' . $visitor->second_name) }}
                        </h3>

                        <!-- Name Tag Preview -->
                        @if($visitor->name_tag)
                            <div class="mb-4">
                                <img src="{{ \App\Models\Visitor::formatImagePath($visitor->name_tag, 'images/name_tags') }}"
                                     alt="Name Tag"
                                     class="w-full h-24 object-contain bg-gray-50 dark:bg-gray-700 rounded border">
                            </div>
                        @endif

                        <!-- Action Button -->
                        @if(!$visitor->receive_nameTag)
                            <button
                                type="button"
                                wire:click="markNameTagReceived({{ $visitor->id }})"
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-black bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 dark:focus:ring-offset-gray-800"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Mark Received
                            </button>
                        @else
                            <button
                                type="button"
                                wire:click="unmarkNameTagReceived({{ $visitor->id }})"
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-orange-300 text-sm font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 dark:bg-orange-900 dark:text-orange-200 dark:border-orange-700 dark:hover:bg-orange-800 dark:focus:ring-offset-gray-800"
                            >
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Unmark Received
                            </button>
                        @endif
                    </div>
                </div>
            @endforeach
        </div>

        @if($this->getVisitors()->isEmpty())
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">No visitors found</h3>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">No attended visitors with name tags match your search criteria.</p>
            </div>
        @endif
    </div>
</x-filament-panels::page>
