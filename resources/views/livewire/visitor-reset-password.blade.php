<div class="reset-password-container">
    <div style="background-color: #ffffff; border-radius: 24px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); overflow: hidden;">
    <div class="reset-header-container">
        <div class="reset-header-padding">
            <div><span class="reset-title-blue">Forgot</span><span class="reset-title-dark"> your password?</span></div>
        </div>
        <div class="reset-subtitle-container">
            <div class="reset-subtitle-text">No worries! Simply enter your email address, and follow the instructions.</div>
        </div>
    </div>
        <div class="reset-form-container">
        <div class="reset-form-inner">
            @if (session()->has('message'))
                <div class="alert alert-success" style="width: 100%; padding: 10px; margin-bottom: 15px; background-color: #d4edda; color: #155724; border-radius: 4px;">
                    {{ session('message') }}
                </div>
            @endif

            @if (session()->has('error'))
                <div class="alert alert-danger" style="width: 100%; padding: 10px; margin-bottom: 15px; background-color: #f8d7da; color: #721c24; border-radius: 4px;">
                    {{ session('error') }}
                </div>
            @endif
            @if (!$otpSent)
                    <form wire:submit.prevent="sendOTP">
                        <div class="reset-form-field-container">
                            <div class="reset-form-field">
                                <div class="reset-form-label-container">
                                    <div class="reset-form-label">Email Address*</div>
                                </div>
                                <div class="reset-input-container">
                                    <input type="email"
                                           wire:model="email"
                                           class="form-control"
                                           style="width: 100%; height: 100%; padding: 15px 24px; border: none; outline: none;"
                                           placeholder="Enter Your Email Address"
                                           required>
                                </div>
                                @error('email')
                                <div class="text-danger" style="color: #dc3545; font-size: 14px; margin-top: 5px;">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="reset-button-container">
                                <button type="submit" class="reset-button" wire:loading.attr="disabled">
                                    <div class="reset-button-text-container">
                                        <div class="reset-button-text" wire:loading.remove wire:target="sendOTP">Reset Password</div>
                                        <div class="reset-button-text" wire:loading wire:target="sendOTP">Sending...</div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </form>
            @endif

            <div class="reset-back-container">
                <a href="{{ route('visitor.login') }}" class="reset-back-link-container">
                    <span class="reset-back-arrow"></span><div class="reset-back-text">Back to Login</div>
                </a>
            </div>
        </div>
    </div>
    </div>
</div>
