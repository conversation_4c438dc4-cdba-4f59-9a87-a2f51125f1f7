<div class="verify-password-page">
    <div class="reset-password-container">
        <div class="reset-header-container">
            <div class="reset-header-padding">
                <div><span class="reset-title-blue">Reset</span><span class="reset-title-dark"> Password</span></div>
            </div>
            <div class="reset-subtitle-container">
                <div class="reset-subtitle-text">Please check your email, We will send a verification code to "<span style="font-weight: 350;">{{ $email }}</span>"</div>
            </div>
        </div>
        <div class="reset-form-container">
            <div class="reset-form-inner">
                @if (session()->has('message'))
                    <div class="alert alert-success" style="width: 100%; padding: 10px; margin-bottom: 15px; background-color: #d4edda; color: #155724; border-radius: 4px;">
                        {{ session('message') }}
                    </div>
                @endif

                @if (session()->has('error'))
                    <div class="alert alert-danger" style="width: 100%; padding: 10px; margin-bottom: 15px; background-color: #f8d7da; color: #721c24; border-radius: 4px;">
                        {{ session('error') }}
                    </div>
                @endif

                <div class="reset-form-field-container">
                    <div class="verify-code-inputs">
                        <div class="verify-code-input-container">
                            <div class="verify-code-input">
                                <div class="verify-code-input-inner">
                                    <input type="text"
                                           wire:model.live="otpDigits.0"
                                           maxlength="1"
                                           class="verify-code-input-number"
                                           wire:keyup="$dispatch('input-digit', { index: 0 })"
                                           autofocus>
                                </div>
                            </div>
                        </div>
                        <div class="verify-code-input-container">
                            <div class="verify-code-input">
                                <div class="verify-code-input-inner">
                                    <input type="text"
                                           wire:model.live="otpDigits.1"
                                           maxlength="1"
                                           class="verify-code-input-number"
                                           wire:keyup="$dispatch('input-digit', { index: 1 })">
                                </div>
                            </div>
                        </div>
                        <div class="verify-code-input-container">
                            <div class="verify-code-input">
                                <div class="verify-code-input-inner">
                                    <input type="text"
                                           wire:model.live="otpDigits.2"
                                           maxlength="1"
                                           class="verify-code-input-number"
                                           wire:keyup="$dispatch('input-digit', { index: 2 })">
                                </div>
                            </div>
                        </div>
                        <div class="verify-code-input-container">
                            <div class="verify-code-input">
                                <div class="verify-code-input-inner">
                                    <input type="text"
                                           wire:model.live="otpDigits.3"
                                           maxlength="1"
                                           class="verify-code-input-number"
                                           wire:keyup="$dispatch('input-digit', { index: 3 })">
                                </div>
                            </div>
                        </div>
                    </div>

                    @error('otp')
                        <div class="text-danger" style="color: #dc3545; font-size: 14px; margin-top: 5px; text-align: center;">
                            {{ $message }}
                        </div>
                    @enderror

                    @error('otpDigits.*')
                        <div class="text-danger" style="color: #dc3545; font-size: 14px; margin-top: 5px; text-align: center;">
                            {{ $message }}
                        </div>
                    @enderror

                    <div class="verify-code-resend">
                        <div class="verify-code-resend-text">Didn't get a code? <span class="verify-code-resend-link" wire:click="resendOTP">Click to resend</span>.</div>
                    </div>

                    <div class="verify-button-container">
                        <button wire:click="verifyOTP" class="verify-button">
                            <div class="verify-button-text">Verify</div>
                        </button>
                    </div>
                </div>

                <div class="reset-back-container">
                    <a href="{{ route('visitor.login') }}" class="reset-back-link-container">

                       <span class="reset-back-arrow"></span><div class="reset-back-text">Back to Login</div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('livewire:initialized', function () {
        // Auto-focus next input after entering a digit
        Livewire.on('input-digit', function (data) {
            const index = data.index;
            if (index < 3 && document.querySelector(`input[wire\\:model\\.live="otpDigits.${index}"]`).value !== '') {
                document.querySelector(`input[wire\\:model\\.live="otpDigits.${index + 1}"]`).focus();
            }
        });
    });
</script>
