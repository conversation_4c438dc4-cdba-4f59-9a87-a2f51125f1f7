<div class="create-password-page">
    <div class="reset-header-container">
        <div class="reset-header-padding">
            <div><span class="reset-title-blue">Reset</span><span class="reset-title-dark"> PASSWORD</span></div>
        </div>
        <div class="reset-subtitle-container">
            <div class="reset-subtitle-text">Please enter your new password</div>
        </div>
    </div>
    
    <div class="reset-form-container">
        <div class="reset-form-inner">
            @if (session()->has('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            <form wire:submit.prevent="updatePassword">
                <div class="reset-form-field-container">
                    <!-- New Password Field -->
                    <div class="create-password-field">
                        <div class="create-password-label">Create New Password</div>
                        <div class="create-password-input-container">
                            <input type="password"
                                   wire:model.live="password"
                                   id="password"
                                   class="create-password-input"
                                   placeholder="Enter New Password">
                            <button type="button"
                                    onclick="togglePasswordVisibility('password')"
                                    class="password-toggle-btn">
                                <i class="fa fa-eye" id="password-toggle-icon"></i>
                            </button>
                        </div>
                        @error('password') 
                            <div class="text-danger">{{ $message }}</div> 
                        @enderror
                    </div>

                    <!-- Confirm Password Field -->
                    <div class="create-password-field">
                        <div class="create-password-label">Confirm new password</div>
                        <div class="create-password-input-container">
                            <input type="password"
                                   wire:model.live="password_confirmation"
                                   id="password_confirmation"
                                   class="create-password-input"
                                   placeholder="Confirm New Password">
                            <button type="button"
                                    onclick="togglePasswordVisibility('password_confirmation')"
                                    class="password-toggle-btn">
                                <i class="fa fa-eye" id="password_confirmation-toggle-icon"></i>
                            </button>
                        </div>
                        @error('password_confirmation')
                            <div class="text-danger">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Update Password Button -->
                    <div class="reset-button-container">
                        <button type="submit" class="reset-button" wire:loading.attr="disabled">
                            <div class="reset-button-text-container">
                                <div class="reset-button-text" wire:loading.remove wire:target="updatePassword">Reset Password</div>
                                <div class="reset-button-text" wire:loading wire:target="updatePassword">Updating...</div>
                            </div>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Make sure password toggle works correctly
        function togglePasswordVisibility(fieldId) {
            const passwordInput = document.getElementById(fieldId);
            const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
            
            if (passwordInput.type === "password") {
                passwordInput.type = "text";
                toggleIcon.classList.remove("fa-eye");
                toggleIcon.classList.add("fa-eye-slash");
            } else {
                passwordInput.type = "password";
                toggleIcon.classList.remove("fa-eye-slash");
                toggleIcon.classList.add("fa-eye");
            }
        }

        // Assign click handlers to password toggle buttons
        document.querySelectorAll('.password-toggle-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const fieldId = this.previousElementSibling.id;
                togglePasswordVisibility(fieldId);
            });
        });
    });

    function togglePasswordVisibility(fieldId) {
        const passwordInput = document.getElementById(fieldId);
        const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
        
        if (passwordInput.type === "password") {
            passwordInput.type = "text";
            toggleIcon.classList.remove("fa-eye");
            toggleIcon.classList.add("fa-eye-slash");
        } else {
            passwordInput.type = "password";
            toggleIcon.classList.remove("fa-eye-slash");
            toggleIcon.classList.add("fa-eye");
        }
    }
</script>
