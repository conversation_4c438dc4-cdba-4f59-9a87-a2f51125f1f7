<div>
    <!-- Hero Section with Video -->
    <section class="hero">
        <div class="video-cover">          <!-- NEW -->
            <iframe class="hero-video-iframe"
                    src="https://player.vimeo.com/video/1103408546?muted=1&amp;autoplay=1&amp;loop=1&amp;transparent=0&amp;background=1&amp;dnt=1&amp;quality=720p&amp;controls=0&amp;title=0&amp;byline=0&amp;portrait=0"
                    title="Hero video" loading="lazy" allow="autoplay; fullscreen"></iframe>
        </div>                              <!-- /NEW -->
        <div class="hero-overlay"></div>
    </section>

    <!-- Navigation Bar -->
    <nav class="nav-bar">
        <div class="container">
            <!-- Add Mobile Toggle Button -->
            <button class="nav-bar-toggle" aria-label="Toggle navigation">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>

            <!-- Navigation Links -->
            <div class="nav-bar-links">
                <a href="#details">Details</a>
                <a href="#benefits">Benefits</a>
                <a href="#newagenda">Agenda</a>
                <a href="#speakers">Speakers</a>
                <a href="#tickets">Tickets</a>
                <a href="#gallery">Gallery</a>
                <a href="#sponsors">Sponsors</a>
                <a href="#venue">Venue</a>
            </div>

            <!-- Action Buttons -->
            <div class="nav-bar-actions">
                @guest('visitor')
                    <a href="{{ route('visitor.login') }}" class="btn btn-login">Login</a>
                    <a href="{{ route('visitor.register') }}" class="btn btn-register">Register</a>
                @else
                    <a href="{{ route('visitor.ticket') }}" class="btn btn-login">Your Ticket</a>
                    <a href="{{ route('logout') }}" class="logout-link"
                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        Logout <i class="bi bi-box-arrow-right"></i>
                    </a>
                    <form id="logout-form" action="{{ route('logout') }}" method="POST" class="d-none">
                        @csrf
                    </form>
                @endguest
            </div>
        </div>
    </nav>
    <!-- Event Details -->
    <section id="details" class="event-details">
        <div class="section-header">Event Details</div>
        <div class="container">
            <div class="event-content">
                <div class="event-image">
                    <img src="{{ asset('images/image-72.png') }}" alt="Event Details">
                </div>
                <div class="event-text">
                    <h2>The Future of Finance MEA | Cairo 2025</h2>
                    <p>Is the perfect opportunity to stay ahead of the curve in the ever-changing world of finance.<br>
                    Join us in the seventh annual Future of Finance
                        conference, to <span style="color: #B05526;">Learn</span>, <span style="color: #F7C100;">Connect</span> and <span
                            style="color: #007DB6;">Grow.</span><br>
                    September 06, 2025 - Keynotes & Panel Discussions<br>
                    <span style="color: #007DB6;">This year's conference will feature a wide range of sessions,
                        including:</span></p>
                </div>
            </div>

            <div class="event-features">
                <div class="feature-card">
                    <div class="feature-title">Workshops</div>
                    <div class="feature-description">Learn new skills and techniques from leading experts in the field
                        of finance.
                    </div>
                </div>
                <div class="feature-card yellow">
                    <div class="feature-title">Exhibition Area</div>
                    <div class="feature-description">Explore the latest products and services from leading financial
                        technology companies.
                    </div>
                </div>
                <div class="feature-card yellow">
                    <div class="feature-title">Roundtables</div>
                    <div class="feature-description">Discuss the latest trends and challenges in finance with other
                        professionals.
                    </div>
                </div>
                <div class="feature-card yellow">
                    <div class="feature-title">Executives' Roundtable</div>
                    <div class="feature-description">Exclusive roundtable discussion with senior financial
                        executives from across Egypt.
                    </div>
                </div>
                <div class="centered-features" style="margin-top: 1rem;">
                    <div class="feature-card">
                        <div class="feature-title">Keynote Speakers</div>
                        <div class="feature-description">Be inspired by keynote speakers from leading organizations.
                        </div>
                    </div>
                    <div class="feature-card blue">
                        <div class="feature-title">MEA Council Annual Meeting</div>
                        <div class="feature-description">Meet with other members of the Finance MEA Council and discuss
                            the
                            future of the profession.
                        </div>
                    </div>
                    <div class="feature-card blue">
                        <div class="feature-title">Panels</div>
                        <div class="feature-description">Hear from experts on a variety of topics, such as the role of
                            banks, and the future of finance.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats -->
    <section class="stats" style="padding:3rem 0;">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number"><b>1000+</b></div>
                    <div class="stat-label">Networking Opportunities</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><b>40+</b></div>
                    <div class="stat-label">Professional Speakers</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><b>30+</b></div>
                    <div class="stat-label">Trending Financial Topics</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><b>35+</b></div>
                    <div class="stat-label">Exhibitors</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number"><b>8+</b></div>
                    <div class="stat-label">Workshops</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Benefits -->
    <section id="benefits" class="benefits">
        <div class="section-header">Event Benefits</div>
        <div class="container">
            <div class="benefits-grid">
                <div class="benefit-card">
                    <div class="benefit-image">
                        <img src="{{ asset('images/Event Benefits/Frame 40085.png') }}" alt="Learn Image"/>
                    </div>
                    <div class="benefit-content">
                        <h3 class="benefit-title">Learn</h3>
                        <p class="benefit-description">Future of Finance MEA | Cairo 2025 offers keynotes, panels, and
                            discussions for valuable insights and networking.</p>
                    </div>
                </div>
                <div class="benefit-card yellow">
                    <div class="benefit-image">
                        <img src="{{ asset('images/Event Benefits/Frame 40085 (1).png') }}" alt="Connect Image"/>
                    </div>
                    <div class="benefit-content">
                        <h3 class="benefit-title">Connect</h3>
                        <p class="benefit-description">Future of Finance MEA | Cairo 2025 offers a prime chance to
                            network with diverse financial professionals and peers.</p>
                    </div>
                </div>
                <div class="benefit-card blue">
                    <div class="benefit-image">
                        <img src="{{ asset('images/Event Benefits/Frame 40085 (2).png') }}" alt="Grow Image"/>
                    </div>
                    <div class="benefit-content">
                        <h3 class="benefit-title">Grow</h3>
                        <p class="benefit-description">The finance industry evolves rapidly. Leaving the office is
                            crucial to avoid stagnation and inspire innovation.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Join Section -->
    <section class="join-section">
        <div class="container join-container">
            <div class="join-text-wrapper">
                <h2 class="join-heading"><b>This year is your ultimate chance to join us</b></h2>
                <p class="join-text" style="text-transform:none;">Become one of our extended family members, and find out the latest news and updates
                    in the world of finance</p>
            </div>
            @if(Auth::guard('visitor')->check())
                <a href="{{route('visitor.ticket')}}" class="btn join-btn">Register now</a>
            @else
                <a href="{{route('visitor.register')}}" class="btn join-btn">Register now</a>
            @endif
        </div>
    </section>

    <section id="newagenda" class="agenda-section">
        <div class="section-header">Event Agenda</div>
        <div class="container">
            <!-- Enhanced Mobile Legend -->
            <div class="mobile-legend">
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color exhibit"></div>
                    <span>Exhibit Hall</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color main"></div>
                    <span>Main Stage</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color game"></div>
                    <span>Game Changers</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color talent"></div>
                    <span>Talent Hub</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color orlov"></div>
                    <span>Orlov Room</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color hope"></div>
                    <span>Hope Room</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color florentine"></div>
                    <span>Florentine Room</span>
                </div>
                <div class="mobile-legend-item">
                    <div class="mobile-legend-color shah"></div>
                    <span>Shah Room</span>
                </div>
            </div>

            <!-- Desktop Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color exhibit"></div>
                    <span>Exhibit Hall</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color main"></div>
                    <span>Main Stage</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color game"></div>
                    <span>Game Changers Stage</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color talent"></div>
                    <span>Talent Hub Stage</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color orlov"></div>
                    <span>Orlov Room</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color hope"></div>
                    <span>Hope Room</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color florentine"></div>
                    <span>Florentine Room</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color shah"></div>
                    <span>Shah Room</span>
                </div>
            </div>

            <!-- Desktop Schedule View -->
            <div class="desktop-schedule">
                <div class="schedule-container">
                    <table class="schedule-table">
                        <thead>
                        <tr>
                            <th style="width: 120px;">Stages</th>
                            <!-- Generate time headers dynamically based on your event times -->
                            @php
                                // Generate time slots from 8:00 to 16:30 in 15-minute intervals
                                $timeSlots = [];
                                for ($hour = 8; $hour <= 16; $hour++) {
                                    for ($minute = 0; $minute < 60; $minute += 15) {
                                        if ($hour == 16 && $minute > 30) break;
                                        $timeSlots[] = sprintf('%d-%02d', $hour, $minute);
                                    }
                                }
                            @endphp

                            @foreach($timeSlots as $timeSlot)
                                @php
                                    list($hour, $minute) = explode('-', $timeSlot);
                                    $displayTime = ($hour > 12) ? ($hour - 12) . ':' . $minute : $hour . ':' . $minute;
                                @endphp
                                <th class="time-cell" data-time="{{ $timeSlot }}">{{ $displayTime }}</th>
                            @endforeach
                        </tr>
                        </thead>
                        <tbody>
                        @for($stage = 1; $stage <= 8; $stage++)
                            <tr>
                                <td class="stage-name stage-{{ $stage }}-header"
                                    style="color:white; vertical-align: middle;">
                                    @php
                                        $stageName = '';
                                        switch($stage) {
                                            case 1: $stageName = 'Exhibit Hall'; break;
                                            case 2: $stageName = 'Main Stage'; break;
                                            case 3: $stageName = 'Game Changers Stage'; break;
                                            case 4: $stageName = 'Talent Hub Stage'; break;
                                            case 5: $stageName = 'Orlov Room'; break;
                                            case 6: $stageName = 'Hope Room'; break;
                                            case 7: $stageName = 'Florentine Room'; break;
                                            case 8: $stageName = 'Shah Room'; break;
                                        }
                                    @endphp
                                    {{ $stageName }}
                                </td>

                                @php
                                    // Get agendas for this stage (day)
                                    $stageAgendas = $agendas->where('day', $stage)->sortBy('start_from');
                                    $currentTime = 8 * 60; // Start at 8:00 AM in minutes
                                    $endTime = 16.5 * 60; // End at 4:30 PM in minutes
                                @endphp

                                @foreach($timeSlots as $timeSlot)
                                    @php
                                        list($hour, $minute) = explode('-', $timeSlot);
                                        $slotTime = $hour * 60 + intval($minute);

                                        // Find agenda that starts at this time
                                        $currentAgenda = null;
                                        foreach($stageAgendas as $agenda) {
                                            $agendaStartTime = date('H', strtotime($agenda->start_from)) * 60 + date('i', strtotime($agenda->start_from));
                                            if ($agendaStartTime == $slotTime) {
                                                $currentAgenda = $agenda;
                                                break;
                                            }
                                        }

                                        // Check if this slot is part of an ongoing event
                                        $isPartOfEvent = false;
                                        foreach($stageAgendas as $agenda) {
                                            $agendaStartTime = date('H', strtotime($agenda->start_from)) * 60 + date('i', strtotime($agenda->start_from));
                                            $agendaEndTime = $agendaStartTime + $agenda->duration;
                                            if ($slotTime > $agendaStartTime && $slotTime < $agendaEndTime) {
                                                $isPartOfEvent = true;
                                                break;
                                            }
                                        }
                                    @endphp

                                    @if($currentAgenda)
                                        @php
                                            // Calculate colspan based on duration
                                            $colspan = ceil($currentAgenda->duration / 15);
                                            $speakerNames = $currentAgenda->speakers->pluck('name')->join(', ');
                                            $startTime = date('h:i A', strtotime($currentAgenda->start_from));
                                            $endTime = date('h:i A', strtotime($currentAgenda->start_from . ' + ' . $currentAgenda->duration . ' minutes'));

                                            // Truncate title to 50 characters for desktop view
                                            $truncatedTitle = strlen($currentAgenda->name) > 40 ? substr($currentAgenda->name, 0, 40) . '...' : $currentAgenda->name;
                                        @endphp

                                        <td data-time="{{ $timeSlot }}" colspan="{{ $colspan }}">
                                            <div class="event stage-{{ $stage }}-event"
                                                 onclick="showEventDetails('{{ addslashes($currentAgenda->name) }}', '{{ addslashes($speakerNames) }}', '{{ addslashes($stageName) }}', '{{ $startTime }} - {{ $endTime }}', '{{ addslashes($currentAgenda->description) }}')">
                                                <div class="event-title">{{ $truncatedTitle }}</div>
                                                <div class="event-time">{{ $startTime }} - {{ $endTime }}</div>
                                                @if($speakerNames)
                                                    <div class="event-presenter">{{ $speakerNames }}</div>
                                                @endif
                                            </div>
                                        </td>
                                    @elseif(!$isPartOfEvent)
                                        <td data-time="{{ $timeSlot }}">
                                            <!-- Empty slot -->
                                        </td>
                                    @endif
                                @endforeach
                            </tr>
                        @endfor
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Enhanced Mobile Schedule View -->
            <div class="mobile-schedule">
                <div id="mobile-events-container">
                    @for($stage = 1; $stage <= 8; $stage++)
                        <div class="mobile-stage-section" data-stage="{{ $stage }}">
                            @php
                                $stageName = '';
                                $stageColor = '';
                                switch($stage) {
                                    case 1:
                                        $stageName = 'Exhibit Hall';
                                        $stageColor = '#002b4a';
                                        break;
                                    case 2:
                                        $stageName = 'Main Stage';
                                        $stageColor = '#007db6';
                                        break;
                                    case 3:
                                        $stageName = 'Game Changers Stage';
                                        $stageColor = '#77cbd5';
                                        break;
                                    case 4:
                                        $stageName = 'Talent Hub Stage';
                                        $stageColor = '#ffcb16';
                                        break;
                                    case 5:
                                        $stageName = 'Orlov Room';
                                        $stageColor = '#9f2378';
                                        break;
                                    case 6:
                                        $stageName = 'Hope Room';
                                        $stageColor = '#f7921e';
                                        break;
                                    case 7:
                                        $stageName = 'Florentine Room';
                                        $stageColor = '#e54b21';
                                        break;
                                    case 8:
                                        $stageName = 'Shah Room';
                                        $stageColor = '#127e5e';
                                        break;
                                }
                            @endphp
                            <h3 class="mobile-stage-header"
                                style="background: linear-gradient(135deg, {{ $stageColor }}, {{ $stageColor }}dd);">{{ $stageName }}</h3>

                            @php
                                $stageEvents = $agendas->where('day', $stage)->sortBy('start_from');
                            @endphp

                            @if($stageEvents->count() > 0)
                                @foreach($stageEvents as $agenda)
                                    @php
                                        $eventStartTime = date('h:i A', strtotime($agenda->start_from));
                                        $eventEndTime = date('h:i A', strtotime($agenda->start_from . ' + ' . $agenda->duration . ' minutes'));
                                        $eventTimeRange = $eventStartTime . ' - ' . $eventEndTime;
                                    @endphp

                                    <div class="mobile-event"
                                         data-stage="{{ $stage }}"
                                         data-start="{{ $agenda->start_from }}"
                                         data-duration="{{ $agenda->duration }}"
                                         style="border-left-color: {{ $stageColor }}">

                                        <div class="mobile-event-time">{{ $eventTimeRange }}</div>
                                        <div class="mobile-event-title">{{ $agenda->name }}</div>
                                        @if($agenda->description)
                                            <div
                                                class="mobile-event-description">{{ Str::limit($agenda->description, 150) }}</div>
                                        @endif

                                        @if($agenda->speakers->count() > 0)
                                            @foreach($agenda->speakers as $speaker)
                                                <div class="speaker-info">
                                                    @if($speaker->speaker_image)
                                                        <img
                                                            src="{{ asset('storage/visitors/'.$speaker->speaker_image) }}"
                                                            alt="{{ $speaker->name }}"
                                                            class="speaker-avatar"
                                                            onerror="this.style.display='none'">
                                                    @else
                                                        <div class="speaker-avatar"
                                                             style="background: {{ $stageColor }}; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 0.8rem;">
                                                            {{ strtoupper(substr($speaker->name, 0, 1)) }}
                                                        </div>
                                                    @endif
                                                    <div class="speaker-details">
                                                        <strong>{{ $speaker->name }}</strong><br>
                                                        @if($speaker->jop_title || $speaker->company)
                                                            <span style="color: #666; font-size: 0.8rem;">
                                                                    @if($speaker->jop_title)
                                                                    {{ $speaker->jop_title }}
                                                                @endif
                                                                @if($speaker->jop_title && $speaker->company)
                                                                    @
                                                                @endif
                                                                @if($speaker->company)
                                                                    {{ $speaker->company }}
                                                                @endif
                                                                </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif
                                    </div>
                                @endforeach
                            @else
                                <div class="no-events">
                                    <div class="no-events-icon">📅</div>
                                    <p>No events scheduled for this stage</p>
                                </div>
                            @endif
                        </div>
                    @endfor
                </div>
            </div>

            <!-- Enhanced Event Details Modal -->
            <div id="eventModal" class="modal">
                <div class="modal-content">
                    <span class="close-modal" onclick="closeModal()">&times;</span>
                    <h3 id="modal-title" class="modal-title">Event Title</h3>
                    <div id="modal-details" class="modal-details">
                        <p><strong></strong> <span id="modal-presenter"></span></p>
                        <p><strong>Location:</strong> <span id="modal-stage"></span></p>
                        <p><strong>Time:</strong> <span id="modal-time"></span></p>
                        <p><strong>Description:</strong> <span id="modal-description"></span></p>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator for Mobile -->
            <div id="mobile-loading" style="display: none; text-align: center; padding: 20px;">
                <div
                    style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid var(--primary-color); border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <p style="margin-top: 10px; color: #666;">Loading schedule...</p>
            </div>

            <style>
                @keyframes spin {
                    0% {
                        transform: rotate(0deg);
                    }
                    100% {
                        transform: rotate(360deg);
                    }
                }

                /* Show mobile legend only on mobile */
                @media (max-width: 768px) {
                    .mobile-legend {
                        display: grid !important;
                    }
                }

                /* Clean design without status indicators */
                @media (max-width: 768px) {
                    .mobile-legend {
                        display: grid !important;
                    }

                    /* Enhanced mobile event cards */
                    .mobile-event {
                        transition: all 0.3s ease;
                    }

                    .mobile-event:hover {
                        transform: translateY(-2px);
                        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
                    }

                    /* Improved stage headers */
                    .mobile-stage-header {
                        position: relative;
                        overflow: hidden;
                    }

                    .mobile-stage-header::before {
                        content: '';
                        position: absolute;
                        top: 0;
                        left: 0;
                        right: 0;
                        bottom: 0;
                        background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
                        animation: shimmer 3s infinite;
                    }

                    @keyframes shimmer {
                        0% {
                            transform: translateX(-100%);
                        }
                        100% {
                            transform: translateX(100%);
                        }
                    }
                }

                /* Enhanced modal for mobile */
                @media (max-width: 768px) {
                    .modal-content {
                        margin: 10px;
                        max-height: 90vh;
                        border-radius: 16px;
                        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                    }

                    .modal-title {
                        font-size: 1.3rem;
                        line-height: 1.4;
                        margin-bottom: 20px;
                        padding-right: 30px;
                    }

                    .close-modal {
                        position: sticky;
                        top: 0;
                        background: white;
                        padding: 10px;
                        margin: -20px -20px 10px -20px;
                        border-bottom: 1px solid #eee;
                        text-align: right;
                    }
                }

                /* Custom scrollbar for mobile */
                @media (max-width: 768px) {
                    .mobile-schedule::-webkit-scrollbar {
                        width: 4px;
                    }

                    .mobile-schedule::-webkit-scrollbar-track {
                        background: #f1f1f1;
                    }

                    .mobile-schedule::-webkit-scrollbar-thumb {
                        background: var(--primary-color);
                        border-radius: 2px;
                    }

                    .mobile-schedule::-webkit-scrollbar-thumb:hover {
                        background: var(--secondary-color);
                    }
                }
            </style>

            <script>
                // Enhanced mobile detection
                function isMobileDevice() {
                    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) || window.innerWidth <= 768;
                }
            </script>
        </div>
    </section>

    <!-- Speakers -->
    <!-- Speakers Section -->
    <!-- Speakers Section -->
    @if(isset($speakers) && $speakers->count())
        <section id="speakers" class="speakers">
            <div class="section-header">Speakers</div>
            <div class="container">
                <div class="speakers-grid">  <!-- ✅ Move this outside the loop -->
                    @foreach($speakers as $speaker)
                        <div class="speaker-card">
                            <div class="speaker-photo">
                                <svg width="152" height="152" viewBox="0 0 152 152" fill="none"
                                     xmlns="http://www.w3.org/2000/svg">
                                    <defs>
                                        <clipPath id="clip-s1">
                                            <path
                                                d="M24.6975 22.1775C-4.89686 50.2821 -6.40814 97.3446 21.6965 126.939C49.801 156.533 96.8789 157.453 126.473 129.348C156.068 101.244 157.579 54.1811 129.474 24.5867C101.37 -5.00769 54.2919 -5.9271 24.6975 22.1775Z"/>
                                        </clipPath>
                                        <linearGradient id="paint-s1" x1="-29.9267" y1="73.3359" x2="181.062" y2="79.38"
                                                        gradientUnits="userSpaceOnUse">
                                            <stop stop-color="#D39429"/>
                                            <stop offset="0.56" stop-color="#FFF9C2"/>
                                            <stop offset="0.81" stop-color="#D29D6B"/>
                                            <stop offset="1" stop-color="#B05526"/>
                                        </linearGradient>
                                    </defs>
                                    <image href="{{asset($speaker->image)}}" width="152" height="152"
                                           clip-path="url(#clip-s1)" preserveAspectRatio="xMidYMid slice"/>
                                    <path
                                        d="M24.6975 22.1775C-4.89686 50.2821 -6.40814 97.3446 21.6965 126.939C49.801 156.533 96.8789 157.453 126.473 129.348C156.068 101.244 157.579 54.1811 129.474 24.5867C101.37 -5.00769 54.2919 -5.9271 24.6975 22.1775Z"
                                        stroke="url(#paint-s1)" stroke-width="3" stroke-miterlimit="10" fill="none"/>
                                </svg>
                                <img src="{{asset($speaker->speaker_image)}}" alt="Speaker Image"/>
                            </div>
                            <div class="speaker-name">{{ $speaker->name }}</div>
                            <div class="speaker-title">{{ $speaker->jop_title }}</div>
                            <div class="speaker-company">{{ $speaker->company }}</div>
                        </div>
                    @endforeach
                </div>
            </div>
        </section>
    @endif

    @if(isset($sponsors) && $sponsors->count())
        <section id="sponsors" class="sponsors-section">
            <div class="section-header">Sponsors</div>
            <div class="container">
                <div class="sponsors-content-wrapper">
                    <!-- Main Sponsors -->
                    @if(isset($sponsorsByTier['Main']) && $sponsorsByTier['Main']->count() > 0)
                        <div class="main-sponsors-section">
                            <div class="sponsors-section-title">Main Sponsors</div>

                            @foreach($sponsorsByTier['Main'] as $sponsor)
                                <img class="main-sponsor-img"
                                     src="{{ asset('storage/' . $sponsor->image) }}" alt="{{ $sponsor->name }}"/>
                            @endforeach
                        </div>
                    @endif
                    <!-- Platinum Sponsors -->
                    @if(isset($sponsorsByTier['Platinum']) && $sponsorsByTier['Platinum']->count() > 0)
                        <!-- Platinum Sponsors -->
                        <div class="platinum-sponsors-section">
                            <div class="sponsors-section-title">Platinum Sponsors</div>
                            <div class="platinum-sponsors-images">
                                @foreach($sponsorsByTier['Platinum'] as $sponsor)
                                    <img src="{{ asset('storage/' . $sponsor->image) }}" alt="{{ $sponsor->name }}"/>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Gold and Silver Sponsors Container -->
                    @if(isset($sponsorsByTier['Gold']) && $sponsorsByTier['Gold']->count() > 0)
                        <div class="gold-silver-container">
                            <!-- Gold Sponsors -->
                            <div class="gold-sponsors-section">
                                <div class="sponsors-section-title">Gold Sponsors</div>
                                <div class="gold-sponsors-images">

                                    @foreach($sponsorsByTier['Gold'] as $sponsor)
                                        <img src="{{ asset('storage/' . $sponsor->image) }}"
                                             alt="{{ $sponsor->name }}"/>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif
                    <!-- Silver Sponsors -->
                    @if(isset($sponsorsByTier['Startup']) && $sponsorsByTier['Startup']->count() > 0)
                        <div class="silver-sponsors-section">
                            <div class="sponsors-section-title">Startup Exhibitors</div>
                            <div class="silver-sponsors-images">

                                @foreach($sponsorsByTier['Startup'] as $sponsor)
                                    <img src="{{ asset('storage/' . $sponsor->image) }}"
                                         alt="{{ $sponsor->name }}"/>
                                @endforeach

                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </section>
    @endif

    <!-- Tickets -->
    <section id="tickets" class="tickets">
        <div class="section-header">Tickets</div>
        <div class="container">
            <div class="tickets-content">
                <div class="tickets-header">
                    <h2 class="tickets-title">Register Now</h2>
                    <p class="tickets-subtitle">We Are Waiting For You</p>
                </div>
                <div class="tickets-grid">
                    @foreach ($tickets as $ticket)
                        <div class="ticket-card">
                            <div class="ticket-header">
                                <div class="ticket-type">{{ ucwords(str_replace('_', ' ', $ticket->name)) }}</div>
                                <div class="ticket-price">${{ $ticket->price }}</div>
                                @if ($ticket->discount_price > 0)
                                    <div class="ticket-original-price">${{ $ticket->discount_price }}</div>
                                @endif
                            </div>
                            <div class="ticket-features">
                                <ul class="ticket-feature-list" style="padding-left: 0rem;">
                                    @foreach (explode("\n", $ticket->description) as $description)
                                        <!-- Split description into lines -->
                                        <li class="ticket-feature">
                                            <div class="ticket-feature-icon" style="background:#F7C100;"></div> <!-- Optional: Add an icon -->
                                            <span>{{ $description }}</span>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                            @if(Auth::guard('visitor')->check())
                                <a href="{{route('visitor.ticket')}}" class="ticket-btn">Book Your Seat</a>
                            @else
                                <a href="{{route('visitor.register')}}" class="ticket-btn">Book Your Seat</a>
                            @endif
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>


    <section id="venue" class="event-venue-section">
        {{-- Header --}}
        <div class="venue-header-container">
            <div class="section-header">Event Venue</div>
        </div>

        {{-- Content --}}
        <div class="venue-content">
            <div class="venue-container">
                {{-- Left Image with Overlay --}}
                <div class="venue-image-left">
                    <img src="{{asset('images/event venue/img.png')}}" alt="Conrad Cairo Hotel"/>
                    <div class="venue-overlay"></div>
                    <div class="venue-text-content">
                        <h3 class="venue-name">Royal Maxim Palace Kempinski, Cairo, Egypt</h3>
                        <p class="venue-description">
                            Standing majestically in the heart of New Cairo, Royal Maxim Palace Kempinski reflects a seamless fusion of Egyptian hospitality and European elegance.
                        </p>
                    </div>
                </div>

                {{-- Right Image --}}
                <div class="venue-image-right">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3456.8312845008495!2d31.41584716954352!3d30.06104620639651!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x14583d66f83959d3%3A0x50aa74e3a1be44b6!2sRoyal%20Maxim%20Palace%20Kempinski%20Cairo!5e0!3m2!1sen!2seg!4v1646834648625!5m2!1sen!2seg"
                        width="100%" frameborder="0" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                </div>
            </div>
        </div>
    </section>

    <!-- resources/views/livewire/gallery.blade.php -->
    <section id="gallery" class="gallery-section">
        <div class="section-header">Gallery</div>
        <div class="container">
        </div>

        {{-- Gallery Content --}}
        <div class="gallery-content">
            <div class="gallery-container">
                <div class="gallery-scroll-wrapper">
                    <div class="gallery-grid">
                        <div class="gallery-item">
                            <img src="{{asset('images/gallery/0G5A6345.jpg')}}" alt="Gallery Image 1"/>
                        </div>
                        <div class="gallery-item">
                            <img src="{{asset('images/gallery/0G5A6415.jpg')}}" alt="Gallery Image 2"/>
                        </div>
                        <div class="gallery-item">
                            <img src="{{asset('images/gallery/7C3A0233 1.jpg')}}" alt="Gallery Image 3"/>
                        </div>
                        <div class="gallery-item">
                            <img src="{{asset('images/gallery/7C3A0255.jpg')}}" alt="Gallery Image 6"/>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title"><b>Secure Your Spot Now - Limited Tickets Available!</b></h2>
                <p class="cta-description">Choose your ticket type and experience world-class networking, knowledge, and
                    growth opportunities with BEACON FinTrain.</p>
                <a href="#register" class="cta-button">Get Your Ticket</a>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="contact-header"><h2>Contact Us</h2></div>
        <div class="container">
            <p class="contact-subtitle">We will accept all your inquiries on the following contacts</p>
            <div class="contact-info d-flex justify-content-center">
                <div class="contact-block">
                    <h3 class="contact-block-title">PHONE NUMBER</h3>
                    <p class="contact-block-text"><i class="bi bi-telephone-fill"></i> <a href="tel:+201276209989">+20 ************</a></p>
                </div>
                <div class="contact-divider"></div>
                <div class="contact-block">
                    <h3 class="contact-block-title">EMAIL</h3>
                    <p class="contact-block-text"><i class="bi bi-envelope-fill"></i> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Branches Section -->
    <section id="branches" class="branches-section">
        <div class="section-header">Our Branches</div>
        <div class="container">
            <div class="branches-container">
                <div class="branch-card">
                    <div class="branch-header">
                        <div class="branch-title">Egypt Office</div>
                        <div class="branch-divider"></div>
                    </div>
                    <div class="branch-content">
                        <p>75-77 Degla Plaza, Degla Street 199, Maadi Assarayat Al Gharbeyah, 6th floor, Maadi, Cairo, Egypt.</p>
                        <p><span class="branch-label">Phone:</span> <a href="tel:+20 ************" class="branch-link">+20 ************</a></p>
                        <p><span class="branch-label">Email:</span> <a href="mailto:<EMAIL>" class="branch-link"><EMAIL></a></p>
                    </div>
                    <div class="branch-image">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3456.8312845008495!2d31.274067515497958!3d29.95553118191463!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x145839409588487f%3A0xbb0828757ba61205!2sBeacon%20FinTrain!5e0!3m2!1sen!2seg!4v1646834648625!5m2!1sen!2seg" width="100%" height="130" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                </div>

                <div class="branch-card">
                    <div class="branch-header">
                        <div class="branch-title">United Arab Emirates Office - Dubai</div>
                        <div class="branch-divider"></div>
                    </div>

                    <div class="branch-content">
                        <p>31st Floor Anantara Downtown-Business Tower, Marassi Drive, Business Bay, Dubai, U.A.E PO BOX 215268</p>
                        <p><span class="branch-label">Phone:</span> <a href="tel:+971 58 100 4098" class="branch-link">+971 58 100 4098</a></p>
                        <p><span class="branch-label">Email:</span> <a href="mailto:<EMAIL>" class="branch-link"><EMAIL></a></p>
                    </div>

                    <div class="branch-image">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3610.5298808103526!2d55.25929884813699!3d25.185347120443083!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f69c21ea69a4b%3A0x57b7c10a13f76ee!2sAnantara%20Business%20Tower!5e0!3m2!1sen!2seg!4v1730807474312!5m2!1sen!2seg" width="100%" height="130" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                </div>

                <div class="branch-card">
                    <div class="branch-header">
                        <div class="branch-title">United Arab Emirates Office - Sharjah</div>
                        <div class="branch-divider"></div>
                    </div>

                    <div class="branch-content">
                        <p>Sharjah Media City, Sharjah, Block A, United Arab Emirates</p>
                        <p><span class="branch-label">Phone:</span> <a href="tel:+971 58 100 4098" class="branch-link">+971 58 100 4098</a></p>
                        <p><span class="branch-label">Email:</span> <a href="mailto:<EMAIL>" class="branch-link"><EMAIL></a></p>
                    </div>

                    <div class="branch-image">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d57738.97974278497!2d55.26928974863279!3d25.24749100000001!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e5f5d5e7183b853%3A0x75b0f60be66fc131!2sSharjah%20Media%20City%20(Shams)%20-%20Dubai%20Branch!5e0!3m2!1sen!2seg!4v1716706828851!5m2!1sen!2seg" width="100%" height="130" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                </div>

                <div class="branch-card">
                    <div class="branch-header">
                        <div class="branch-title">Saudi Arabia Office</div>
                        <div class="branch-divider"></div>
                    </div>

                    <div class="branch-content">
                        <p>Business Valley 4554 anas bin malik 3rd floor, Riyadh 13524, Saudi Arabia.</p>
                        <p><span class="branch-label">Phone:</span> <a href="tel:+966 50 119 5720" class="branch-link">+966 50 119 5720</a></p>
                        <p><span class="branch-label">Email:</span> <a href="mailto:<EMAIL>" class="branch-link"><EMAIL></a></p>
                    </div>

                    <div class="branch-image">
                        <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d14486.273522849597!2d46.6212308!3d24.810229!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3e2ee5a6607a6b99%3A0xe38df2d43df9067f!2sAFP%20Exclusive%20MEA%20Partner%20%7C%20Beacon%20FinTrain!5e0!3m2!1sen!2seg!4v1712051265972!5m2!1sen!2seg" width="100%" height="130" style="border:0;" allowfullscreen="" loading="lazy"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
