<div>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h2 class="text-primary fw-bold">Access Your Account or Join Us Today</h2>
                    <p class="text-muted">create a new account to explore exciting opportunities!</p>
                </div>

                <!-- Login/Signup Tabs -->
                <div class="tabs-container auth-tabs-container mb-4">
                    <div class="row g-0" style="border: 1px solid #dee2e6; width: 628px; margin: 0 auto;">
                        <div class="col-6">
                            <a href="{{ route('visitor.login') }}" class="btn w-100 py-3 text-dark"
                               style="background-color: white; border: 1px solid #102649; border-radius: 0;">Existing User? Login</a>
                        </div>
                        <div class="col-6">
                            <a href="{{ route('visitor.register') }}" class="btn w-100 py-3 text-white"
                               style="background-color: #102649; border: 1px solid #102649; border-radius: 0;">New User? Sign Up</a>
                        </div>
                    </div>
                </div>

                <!-- Main Card -->
                <div class="card shadow-sm border-light rounded-4 p-4 p-md-5">
                    @if(!$isValidCode && $code)
                        <div class="alert alert-danger">{{ $errorMessage }}</div>
                    @endif
                    <!-- Progress Steps -->
                    <x-visitor-registration.progress-steps :currentStep="$currentStep" />

                    @if($currentStep == 1)
                        <form wire:submit.prevent="nextStep">
                            @include('components.visitor-registration.basic-information', [
                                'countries' => $countries,
                                'selectedCountryCallingCode' => $selectedCountryCallingCode,
                                'profilePhoto' => $profilePhoto ?? null,
                                'country' => $country,
                                'flagClass' => $flagClass,
                                'workshops' => $workshops,
                                'showWorkshopSelection' => $code && $isValidCode
                            ])
                            <div class="d-grid mt-4">
                                <button type="submit" class="btn btn-primary py-3 rounded-3">Continue</button>
                            </div>
                        </form>
                    @elseif($currentStep == 2)
                        <div>
                            <x-visitor-registration.company-information
                                :sectors="$sectors"
                                :countries="$countries"
                                :selectedCountryCallingCode="$selectedCountryCallingCode"
                            />
                            <div class="d-flex gap-3 mt-4">
                                <button wire:click="previousStep" type="button" class="btn btn-outline-primary py-3 px-4 rounded-3 flex-grow-1">
                                    <i class="bi bi-arrow-left me-2"></i> Back
                                </button>
                                <button wire:click="nextStep" type="button" class="btn btn-primary py-3 px-5 rounded-3 flex-grow-1">
                                    Continue <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                            </div>
                        </div>
                    @elseif($currentStep == 3)
                        <div>
                            <x-visitor-registration.review-submit
                                :firstName="$firstName"
                                :lastName="$lastName"
                                :email="$email"
                                :phone="$phone"
                                :nationality="$nationality"
                                :selectedCountryCallingCode="$selectedCountryCallingCode"
                                :companyName="$companyName"
                                :jobTitle="$jobTitle"
                                :jobTitle2="$jobTitle2"
                                :industry="$industry"
                                :linkedin="$linkedin"
                                :profilePhoto="$profilePhoto"
                            />
                        </div>
                    @endif
                </div>

                <!-- Footer -->
                <div class="mt-4 text-center">
                    <p class="small text-muted">
                        By continuing, you agree to the <a href="#" class="text-decoration-none">Terms of use</a> and <a href="#" class="text-decoration-none">Privacy Policy</a>.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Include JavaScript -->
    @vite(['resources/js/app.js'])
</div>
