
<div class="py-12">
    <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        {{-- Flash Messages --}}
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
            <div class="p-6 bg-white border-b border-gray-200">
                <div>
                    @if($visitor && $visitor->visitorTickets()->first() !== null)
                        {{-- Check if visitor is loaded and has any ticket at all --}}
                        @if(($ticket && $ticket->payment_status === 0) || ($ticket->payment_status === 1 && !$visitor->name_tag))
                            {{-- $ticket from Livewire is the first PENDING ticket --}}
                            <div class="payment-details-container">
                                <div class="payment-details-content">
                                    <div class="payment-details-header">
                                        <span class="dark">Your</span>
                                        <span class="light">ticket</span>
                                        <span class="dark">Payment Details</span>
                                    </div>

                                    <div class="payment-details-greeting">
                                        <span class="greeting-light">Dear</span>
                                        <span class="greeting-dark">{{ $visitor->name }}<br>Please confirm your ticket details and proceed securely to payment.</span>
                                    </div>

                                    <div class="ticket-info">
                                        <div class="ticket-info-row">
                                            <span class="ticket-info-label">Ticket Type:</span>
                                            <span class="ticket-info-value">{{ $ticket->ticket->name }}</span>
                                        </div>
                                        <div class="ticket-info-row">
                                            <span class="ticket-info-label">Total Ticket Price:</span>
                                            <span
                                                class="ticket-info-value">${{ number_format($ticket->ticket->price, 2) }}</span>
                                        </div>
                                        <div class="ticket-info-row">
                                            <span class="ticket-info-label">Ticket Benefits</span>
                                            <br>
                                            @php
                                                // Split description on new lines OR commas
                                                $benefits = preg_split('/\r?\n|,/', $ticket->ticket->description, -1, PREG_SPLIT_NO_EMPTY);
                                            @endphp

                                            <ul class="ticket-benefits mx-auto">
                                                @foreach ($benefits as $benefit)
                                                    <li>
                                                        {{-- SVG bullet --}}
                                                        <svg width="18" height="19" viewBox="0 0 18 19" fill="none"
                                                             xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                            <rect y="0.5" width="18" height="18" rx="9" fill="#007DB6"/>
                                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                                                                          d="M12.8225 6.0425L7.4525 11.225L6.0275 9.7025C5.765 9.455 5.3525 9.44
                                                                 5.0525 9.65C4.76 9.8675 4.6775 10.25 4.8575 10.5575L6.545 13.3025C6.71
                                                                 13.5575 6.995 13.715 7.3175 13.715C7.625 13.715 7.9175 13.5575 8.0825
                                                                 13.3025C8.3525 12.95 13.505 6.8075 13.505 6.8075C14.18 6.1175 13.3625
                                                                 5.51 12.8225 6.035V6.0425Z"
                                                                  fill="white"/>
                                                        </svg>

                                                        {{-- Text --}}
                                                        <span>{{ trim($benefit) }}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>

                                    {{-- Unified Payment Button --}}
                                    <div class="payment-button-container">
                                        <button wire:click="purchaseTicket" class="payment-button"
                                                wire:loading.attr="disabled" wire:target="purchaseTicket">
                                            <span wire:loading wire:target="purchaseTicket"
                                                  class="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-r-2 border-white mr-2"></span>
                                            <span wire:loading.remove wire:target="purchaseTicket">
                                                @if($ticket->ticket->type === 'free' || $ticket->ticket_price == 0)
                                                    Submit
                                                @else
                                                    Proceed to Payment
                                                @endif
                                            </span>
                                            <span wire:loading wire:target="purchaseTicket">Processing...</span>
                                        </button>
                                    </div>

                                    <div class="payment-notes">
                                        <div class="payment-note">You will receive a confirmation email after payment.
                                        </div>
                                        <div class="payment-note">This page is secured and your information is
                                            protected.
                                        </div>
                                    </div>

                                    <div class="payment-info-section">
                                        <div class="payment-info-header">More Information</div>

                                        <div class="payment-status-section">
                                            <div>
                                                <div class="payment-status-label">Status</div>
                                                <div class="payment-status-value">
                                                    {{-- $ticket->payment_status is 0 here --}}
                                                    Waiting for Purchase
                                                </div>
                                            </div>

                                            <div>
                                                <div class="confirmation-number-label">Confirmation Number</div>
                                                {{-- QR code might exist on visitor even if this specific ticket isn't confirmed yet --}}
                                                <div
                                                    class="confirmation-number-value">{{ $visitor->qr_code ?? 'Will be generated after payment' }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @else
                            {{-- Visitor has tickets, but no PENDING one (e.g., $ticket is null or not status 0). Show info for paid ticket if any. --}}
                            @php
                                $anyPaidTicket = $visitor->visitorTickets()
                                    ->where('payment_status', 1)
                                    ->with('ticket') // Eager load ticket type
                                    ->orderBy('updated_at', 'desc')
                                    ->first();
                            @endphp
                            @if($anyPaidTicket)
                                <div class="p-6">
                                    @livewire('visitor-name-tag', ['visitor' => $visitor, 'ticket' => $anyPaidTicket])
                                </div>
                            @else
                                {{-- Visitor has tickets, but none are pending and none are paid with status 1. This is an unusual state. --}}
                                <div class="text-center p-6">
                                    <p>Thank you for your registration. Your ticket status is currently under review.
                                        Please contact support if you have questions.</p>
                                </div>
                            @endif
                        @endif
                    @elseif($visitor && $visitor->visitorTickets()->count() === 0)
                        {{-- Visitor is loaded, but has no tickets at all --}}
                        <div class="container mt-4">
                            <div class="ticket-wrapper">
                                <div class="ticket-card"
                                     style="max-width: 800px; padding: 40px 30px; border-radius: 20px; box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);">
                                    <div style="text-align: center; margin-bottom: 20px;">
                                        <div class="ticket-title">
                                            <span class="my-text"
                                                  style="color: #007DB6; font-size: 28px; font-weight: 600;">MY</span>
                                            <span class="ticket-text"
                                                  style="color: #102649; font-size: 28px; font-weight: 600;">TICKET</span>
                                        </div>
                                    </div>

                                    <div style="text-align: center; margin-bottom: 20px;">
                                        <div class="ticket-message">
                                            <span class="dear-text" style="color: #007DB6; font-size: 16px;">Dear</span>
                                            <span class="details-text"
                                                  style="color: #007DB6; font-size: 16px; font-weight: 600;">{{ $visitor->name }}</span>
                                        </div>
                                    </div>

                                    <div style="text-align: center; margin-bottom: 15px;">
                                        <div class="ticket-details" style="color: #555; font-size: 16px;">
                                            Thank you for registering for AFP Future of Finance | Cairo 2025 Conference
                                        </div>
                                    </div>

                                    <div style="text-align: center; margin-bottom: 15px;">
                                        <div class="ticket-details" style="color: #555; font-size: 16px;">
                                            Your registration form is currently being reviewed.
                                        </div>
                                    </div>

                                    <div style="text-align: center; margin-bottom: 30px;">
                                        <div class="ticket-details" style="color: #555; font-size: 16px;">
                                            A member of our team will contact you shortly.
                                        </div>
                                    </div>

                                    @if($visitor && !$visitor->hasVerifiedEmail())
                                        <div
                                            style="background-color: #fff9e6; padding: 15px; border-radius: 8px; margin-bottom: 30px; text-align: center;">
                                            <div
                                                style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                                                <i class="bi bi-exclamation-triangle-fill"
                                                   style="color: #f0c000; margin-right: 10px;"></i>
                                                <div style="color: #555; font-size: 16px;">
                                                    Please verify your email address to complete your registration.
                                                </div>
                                            </div>
                                            <div>
                                                <button class="btn btn-sm btn-outline-warning"
                                                        onclick="window.location.href='{{ route('visitor.verification.notice') }}'"
                                                        style="padding: 5px 15px; border-color: #007DB6; color: #007DB6;">
                                                    Resend Verification Email
                                                </button>
                                            </div>
                                        </div>
                                    @endif

                                    <div style="text-align: center;">
                                        <a href="{{url('/#newagenda')}}" class="agenda-button"
                                           style="background-color: #102649; color: white; padding: 14px 40px; border-radius: 8px; text-decoration: none; display: inline-block; font-weight: 500; font-size: 16px;">Check Our Agenda</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @else
                        {{-- Visitor not loaded (e.g., not logged in) or some other edge case --}}
                        <div class="text-center p-6">
                            <p>Please <a href="{{ route('visitor.login') }}" class="text-blue-500 hover:underline">log
                                    in</a> to view your ticket details.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
