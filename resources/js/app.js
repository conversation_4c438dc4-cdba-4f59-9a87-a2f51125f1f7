// Load emergency fix for visitor name tag infinite loop first
import './visitor-name-tag-fix';
import './bootstrap';
import './countdown';
import './otp-input';
import './home.js';

// Initialize Bootstrap components
import * as bootstrap from 'bootstrap';

// Conditionally load the appropriate scripts based on page context
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all Bootstrap tooltips and popovers
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

    // Initialize all dropdowns
    const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
    const dropdownList = [...dropdownElementList].map(dropdownToggle => new bootstrap.Dropdown(dropdownToggle));

    // Check if we're on a visitor name tag page
    const isVisitorNameTagPage = document.querySelector('.ticket-container') &&
        document.querySelector('.ticket-preview');

    // Check if we're on a visitor registration page
    // NOTE: CSS attribute selectors treat colon (:) and dot (.) as special characters
    // so they must be escaped **within** the selector string. Because this string is
    // inside JavaScript source, each backslash itself needs to be escaped as `\\` so
    // that it reaches the browser as a single backslash. Failing to do so causes
    // `querySelector` to throw a `SyntaxError` which aborts the rest of the script and
    // results in visitor-registration assets being loaded on the name-tag page.

    const registrationFormSelector = 'form[wire\\:submit\\.prevent="register"]';
    let isRegistrationPage = false;
    try {
        isRegistrationPage = document.querySelector(registrationFormSelector) !== null ||
            document.querySelector('.registration-form') !== null;
    } catch (e) {
        // Defensive: If the selector fails for any reason, fall back to checking the
        // `.registration-form` class only and log the error for debugging.
        console.warn('Registration form selector failed, falling back to class check:', e);
        isRegistrationPage = document.querySelector('.registration-form') !== null;
    }

    // Apply emergency fix again when DOM is loaded
    if (typeof clearAllIntervals === 'function') {
        clearAllIntervals();
        console.log('Applied interval clearing on DOMContentLoaded');
    }

    if (isVisitorNameTagPage) {
        console.log('Detected visitor name tag page - loading specialized cropper');

        // Load visitor name tag cropper only
        import('./visitor-name-tag-cropper')
            .then(() => {
                console.log('Visitor name tag cropper loaded successfully');

                // Ensure profilePhoto.js is not initialized
                if (window.CairoBeaconEvent && window.CairoBeaconEvent.profilePhoto) {
                    window.CairoBeaconEvent.profilePhoto.init = function() {
                        console.log('profilePhoto.init disabled on visitor name tag page');
                        return false;
                    };
                }
            })
            .catch(err => console.error('Error loading visitor name tag cropper:', err));
    } else if (isRegistrationPage) {
        console.log('Detected registration page - loading visitor registration');

        // Load visitor registration (which includes profilePhoto.js)
        import('./visitor-registration')
            .then(() => console.log('Visitor registration loaded successfully'))
            .catch(err => console.error('Error loading visitor registration:', err));
    } else {
        console.log('Not on a special page - no additional scripts loaded');
    }
});
