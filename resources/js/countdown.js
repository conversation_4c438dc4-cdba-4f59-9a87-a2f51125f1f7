// Countdown timer functionality with localStorage support
document.addEventListener('DOMContentLoaded', function() {
    // Set the date we're counting down to
    const eventDate = "Sep 6, 2025 09:00:00";
    const countDownDate = new Date(eventDate).getTime();
    
    // Store the target date in localStorage if not already set
    if (!localStorage.getItem('eventTargetDate')) {
        localStorage.setItem('eventTargetDate', eventDate);
    }
    
    // Function to update the countdown display
    function updateCountdown() {
        // Get today's date and time
        var now = new Date().getTime();

        // Find the distance between now and the count down date
        var distance = countDownDate - now;

        // Time calculations for days, hours, minutes and seconds
        var days = Math.floor(distance / (1000 * 60 * 60 * 24));
        var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        var seconds = Math.floor((distance % (1000 * 60)) / 1000);

        // Check if the elements exist before updating
        const daysEl = document.getElementById("days");
        const hoursEl = document.getElementById("hours");
        const minutesEl = document.getElementById("minutes");
        const secondsEl = document.getElementById("seconds");

        if (daysEl && hoursEl && minutesEl && secondsEl) {
            // Store the current countdown values in localStorage
            localStorage.setItem('countdownDays', days);
            localStorage.setItem('countdownHours', hours);
            localStorage.setItem('countdownMinutes', minutes);
            localStorage.setItem('countdownSeconds', seconds);
            
            // Display the result with padded zeros
            daysEl.innerHTML = days.toString().padStart(2, '0');
            hoursEl.innerHTML = hours.toString().padStart(2, '0');
            minutesEl.innerHTML = minutes.toString().padStart(2, '0');
            secondsEl.innerHTML = seconds.toString().padStart(2, '0');
        }

        // If the count down is finished, write zeros
        if (distance < 0) {
            if (daysEl && hoursEl && minutesEl && secondsEl) {
                daysEl.innerHTML = "00";
                hoursEl.innerHTML = "00";
                minutesEl.innerHTML = "00";
                secondsEl.innerHTML = "00";
            }
            
            // Clear interval if countdown is finished
            if (window.countdownInterval) {
                clearInterval(window.countdownInterval);
                window.countdownInterval = null;
            }
        }
    }
    
    // Run updateCountdown immediately to initialize the display
    updateCountdown();
    
    // Try to restore previous values from localStorage while waiting for the first update
    function restoreFromLocalStorage() {
        const daysEl = document.getElementById("days");
        const hoursEl = document.getElementById("hours");
        const minutesEl = document.getElementById("minutes");
        const secondsEl = document.getElementById("seconds");
        
        if (daysEl && hoursEl && minutesEl && secondsEl) {
            const savedDays = localStorage.getItem('countdownDays');
            const savedHours = localStorage.getItem('countdownHours');
            const savedMinutes = localStorage.getItem('countdownMinutes');
            const savedSeconds = localStorage.getItem('countdownSeconds');
            
            if (savedDays) daysEl.innerHTML = savedDays.toString().padStart(2, '0');
            if (savedHours) hoursEl.innerHTML = savedHours.toString().padStart(2, '0');
            if (savedMinutes) minutesEl.innerHTML = savedMinutes.toString().padStart(2, '0');
            if (savedSeconds) secondsEl.innerHTML = savedSeconds.toString().padStart(2, '0');
        }
    }
    
    // First try to restore from localStorage
    restoreFromLocalStorage();
    
    // Update the count down every 1 second
    window.countdownInterval = setInterval(updateCountdown, 1000);
});
