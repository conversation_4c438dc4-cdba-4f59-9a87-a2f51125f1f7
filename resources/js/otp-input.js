/**
 * OTP Input Field Functionality
 *
 * Handles automatic focus movement between OTP input fields,
 * digit validation, and paste functionality.
 */
document.addEventListener('livewire:initialized', () => {
    // Get all OTP input fields
    const otpInputs = document.querySelectorAll('.verify-code-input-number');

    console.log(otpInputs);

    // Add event listeners for each input field
    otpInputs.forEach((input, index) => {
        // Handle input event (when a digit is entered)
        input.addEventListener('input', function(e) {
            // If a digit is entered (length is 1) and there's a next input, focus on it
            if (this.value.length === 1 && index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }

            // Ensure only numbers are entered
            if (!/^\d*$/.test(this.value)) {
                this.value = this.value.replace(/\D/g, '');
            }

            // Update Livewire model manually to ensure state is synchronized
            Livewire.dispatch('input', {
                name: this.getAttribute('wire:model.live').replace('wire:model.live=', '').replace(/"/g, ''),
                value: this.value
            });
        });

        // Handle keydown event (for backspace/delete)
        input.addEventListener('keydown', function(e) {
            // If backspace is pressed and the field is empty, focus on the previous field
            if ((e.key === 'Backspace' || e.key === 'Delete') && this.value.length === 0 && index > 0) {
                otpInputs[index - 1].focus();
            }
        });

        // Handle paste event to distribute digits across fields
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = (e.clipboardData || window.clipboardData).getData('text');
            const digits = pastedData.replace(/\D/g, '').split('');

            // Fill current and subsequent inputs with pasted digits
            digits.forEach((digit, i) => {
                if (index + i < otpInputs.length) {
                    const currentInput = otpInputs[index + i];
                    currentInput.value = digit;
                    // Dispatch an 'input' event to ensure Livewire picks up the change
                    currentInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
            });

            // Focus on the next empty field or the last field
            const nextFocusIndex = Math.min(index + digits.length, otpInputs.length - 1);
            if (otpInputs[nextFocusIndex]) {
                otpInputs[nextFocusIndex].focus();
            }
        });
    });
});
