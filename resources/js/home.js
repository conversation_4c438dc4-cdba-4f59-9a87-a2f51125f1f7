document.addEventListener('DOMContentLoaded', function() {
    // Get gallery elements
    const scrollWrapper = document.querySelector('.gallery-scroll-wrapper');
    const galleryGrid = document.querySelector('.gallery-grid');
    const galleryItems = document.querySelectorAll('.gallery-item');

    // Check if gallery exists
    if (!scrollWrapper || !galleryGrid || galleryItems.length === 0) return;

    // Configuration
    const scrollSpeed = 1; // pixels per frame
    const pauseDuration = 1000; // pause at end before reversing (milliseconds)
    let scrollDirection = 1; // 1 for forward, -1 for backward
    let isPaused = false;
    let animationId = null;

    // Auto scroll function
    function autoScroll() {
        if (!isPaused) {
            // Get current scroll position
            const currentScroll = scrollWrapper.scrollLeft;
            const maxScroll = scrollWrapper.scrollWidth - scrollWrapper.clientWidth;

            // Check if reached the end
            if (currentScroll >= maxScroll && scrollDirection === 1) {
                isPaused = true;
                setTimeout(() => {
                    scrollDirection = -1;
                    isPaused = false;
                }, pauseDuration);
            }
            // Check if reached the beginning
            else if (currentScroll <= 0 && scrollDirection === -1) {
                isPaused = true;
                setTimeout(() => {
                    scrollDirection = 1;
                    isPaused = false;
                }, pauseDuration);
            }
            // Continue scrolling
            else {
                scrollWrapper.scrollLeft += scrollSpeed * scrollDirection;
            }
        }

        // Continue animation
        animationId = requestAnimationFrame(autoScroll);
    }

    // Start auto-scrolling
    autoScroll();

    // Pause on hover
    scrollWrapper.addEventListener('mouseenter', () => {
        cancelAnimationFrame(animationId);
    });

    // Resume on mouse leave
    scrollWrapper.addEventListener('mouseleave', () => {
        autoScroll();
    });

    // Pause on touch
    scrollWrapper.addEventListener('touchstart', () => {
        cancelAnimationFrame(animationId);
    });

    // Resume after touch
    scrollWrapper.addEventListener('touchend', () => {
        setTimeout(() => {
            autoScroll();
        }, 1000); // Wait 1 second after touch before resuming
    });
});

// =====================================================================
// Agenda Mobile & Desktop Schedule Logic
// =====================================================================

// Mobile Schedule Smart Features
class MobileSchedule {
    constructor() {
        this.currentView = 'timeline';
        this.selectedStages = new Set(['all']);
        this.currentTime = new Date();
        this.init();
    }

    init() {
        this.setupMobileNavigation();
        this.setupStageFiltering();
        this.setupSwipeGestures();
        this.setupQuickActions();
    }

    setupMobileNavigation() {
        // Create mobile navigation if it doesn't exist
        if (!document.querySelector('.mobile-nav')) {
            const mobileSchedule = document.querySelector('.mobile-schedule');
            if (mobileSchedule) {
                const nav = this.createMobileNav();
                mobileSchedule.insertBefore(nav, mobileSchedule.firstChild);
            }
        }

        // View toggle functionality
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('mobile-view-btn')) {
                this.switchView(e.target.dataset.view);
            }
        });
    }

    createMobileNav() {
        const nav = document.createElement('div');
        // Placeholder for future nav HTML if needed
        return nav;
    }

    setupStageFiltering() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('mobile-stage-btn')) {
                const stage = e.target.dataset.stage;
                this.toggleStageFilter(stage, e.target);
            }
        });
    }

    toggleStageFilter(stage, button) {
        // Update button states
        if (stage === 'all') {
            document.querySelectorAll('.mobile-stage-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            button.classList.add('active');
            this.selectedStages = new Set(['all']);
        } else {
            // Remove 'all' if specific stage is selected
            const allBtn = document.querySelector('.mobile-stage-btn[data-stage="all"]');
            if (allBtn) allBtn.classList.remove('active');

            button.classList.toggle('active');

            if (button.classList.contains('active')) {
                this.selectedStages.add(stage);
                this.selectedStages.delete('all');
            } else {
                this.selectedStages.delete(stage);
            }

            // If no stages selected, select all
            if (this.selectedStages.size === 0) {
                this.selectedStages.add('all');
                if (allBtn) allBtn.classList.add('active');
            }
        }

        this.applyStageFilter();
    }

    applyStageFilter() {
        if (this.currentView === 'timeline') {
            this.filterTimelineView();
        } else {
            this.filterStageView();
        }
    }

    filterTimelineView() {
        const timelineEvents = document.querySelectorAll('.timeline-event');
        timelineEvents.forEach(event => {
            const stage = event.dataset.stage;
            if (this.selectedStages.has('all') || this.selectedStages.has(stage)) {
                event.style.display = 'block';
            } else {
                event.style.display = 'none';
            }
        });

        // Hide timeline slots with no visible events
        this.hideEmptyTimeSlots();
    }

    filterStageView() {
        const stageSections = document.querySelectorAll('.mobile-stage-section');
        stageSections.forEach(section => {
            const stage = section.dataset.stage;
            if (this.selectedStages.has('all') || this.selectedStages.has(stage)) {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
        });
    }

    hideEmptyTimeSlots() {
        const timelineSlots = document.querySelectorAll('.timeline-slot');
        timelineSlots.forEach(slot => {
            const visibleEvents = slot.querySelectorAll('.timeline-event:not([style*="display: none"])');
            if (visibleEvents.length === 0) {
                slot.style.display = 'none';
            } else {
                slot.style.display = 'block';
            }
        });
    }

    switchView(view) {
        this.currentView = view;

        // Update button states
        document.querySelectorAll('.mobile-view-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        const activeBtn = document.querySelector(`[data-view="${view}"]`);
        if (activeBtn) activeBtn.classList.add('active');

        // Show/hide appropriate content
        this.showView(view);
        this.applyStageFilter();
    }

    showView(view) {
        if (view === 'timeline') {
            this.showTimelineView();
        } else {
            this.showStageView();
        }
    }

    showTimelineView() {
        // Hide stage view
        const stageView = document.querySelector('.mobile-stage-view');
        if (stageView) {
            stageView.classList.remove('active');
        }

        // Show/create timeline view
        let timelineView = document.querySelector('.mobile-timeline-view');
        if (!timelineView) {
            timelineView = this.createTimelineView();
            const container = document.getElementById('mobile-events-container');
            if (container) {
                container.appendChild(timelineView);
            }
        }
        timelineView.classList.remove('hidden');
    }

    showStageView() {
        // Hide timeline view
        const timelineView = document.querySelector('.mobile-timeline-view');
        if (timelineView) {
            timelineView.classList.add('hidden');
        }

        // Show stage view
        let stageView = document.querySelector('.mobile-stage-view');
        if (!stageView) {
            // Convert existing stage sections to new format
            const existingSections = document.querySelectorAll('.mobile-stage-section');
            if (existingSections.length > 0) {
                stageView = document.createElement('div');
                stageView.className = 'mobile-stage-view';
                existingSections.forEach(section => {
                    stageView.appendChild(section.cloneNode(true));
                });
                const container = document.getElementById('mobile-events-container');
                if (container) {
                    container.appendChild(stageView);
                }
            }
        }
        if (stageView) {
            stageView.classList.add('active');
        }
    }

    createTimelineView() {
        const timelineView = document.createElement('div');
        timelineView.className = 'mobile-timeline-view';

        // Group events by time
        const eventsByTime = this.groupEventsByTime();

        Object.keys(eventsByTime).sort().forEach(time => {
            const slot = document.createElement('div');
            slot.className = 'timeline-slot';

            const timeHeader = document.createElement('div');
            timeHeader.className = 'timeline-time';
            timeHeader.textContent = this.formatTime(time);
            slot.appendChild(timeHeader);

            const eventsContainer = document.createElement('div');
            eventsContainer.className = 'timeline-events';

            eventsByTime[time].forEach(event => {
                const eventElement = this.createTimelineEvent(event);
                eventsContainer.appendChild(eventElement);
            });

            slot.appendChild(eventsContainer);
            timelineView.appendChild(slot);
        });

        return timelineView;
    }

    groupEventsByTime() {
        const eventsByTime = {};
        const events = document.querySelectorAll('.mobile-event');

        events.forEach(event => {
            const timeText = event.querySelector('.mobile-event-time')?.textContent || '';
            const startTime = timeText.split(' - ')[0];

            if (startTime) {
                if (!eventsByTime[startTime]) {
                    eventsByTime[startTime] = [];
                }

                // Extract event data
                const eventData = {
                    title: event.querySelector('.mobile-event-title')?.textContent || '',
                    time: timeText,
                    description: event.querySelector('.mobile-event-description')?.textContent || '',
                    stage: this.getEventStage(event),
                    speakers: this.extractSpeakers(event),
                    element: event
                };

                eventsByTime[startTime].push(eventData);
            }
        });

        return eventsByTime;
    }

    getEventStage(eventElement) {
        // Find parent stage section
        const stageSection = eventElement.closest('.mobile-stage-section');
        if (stageSection) {
            const stageHeader = stageSection.querySelector('.mobile-stage-header');
            return stageHeader ? stageHeader.textContent.trim() : '';
        }
        return '';
    }

    extractSpeakers(eventElement) {
        const speakers = [];
        const speakerElements = eventElement.querySelectorAll('.speaker-info');

        speakerElements.forEach(speakerEl => {
            const name = speakerEl.querySelector('strong')?.textContent || '';
            const avatar = speakerEl.querySelector('.speaker-avatar')?.src || '';
            const details = speakerEl.querySelector('.speaker-details')?.textContent || '';

            if (name) {
                speakers.push({ name, avatar, details });
            }
        });

        return speakers;
    }

    createTimelineEvent(eventData) {
        const eventEl = document.createElement('div');
        eventEl.className = 'timeline-event';
        eventEl.dataset.stage = this.getStageNumber(eventData.stage);

        // Apply stage-specific styling
        const stageNum = this.getStageNumber(eventData.stage);
        eventEl.style.borderLeftColor = this.getStageColor(stageNum);

        eventEl.innerHTML = `
            <div class="timeline-event-header">
                <div class="timeline-event-title">${eventData.title}</div>
                <div class="timeline-event-duration">${this.getEventDuration(eventData.time)}</div>
            </div>
            <div class="timeline-event-stage" style="background-color: ${this.getStageColor(stageNum)}">${eventData.stage}</div>
            ${eventData.speakers.length > 0 ? `
                <div class="timeline-event-speakers">
                    ${eventData.speakers.map(speaker => `
                        <div class="timeline-speaker">
                            ${speaker.avatar ? `<img src="${speaker.avatar}" alt="${speaker.name}" class="timeline-speaker-avatar">` : ''}
                            <span>${speaker.name}</span>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
        `;

        // Add click handler
        eventEl.addEventListener('click', () => {
            this.showEventDetails(eventData);
        });

        return eventEl;
    }

    getStageNumber(stageName) {
        const stageMap = {
            'Exhibit Hall': '1',
            'Main Stage': '2',
            'Game Changers Stage': '3',
            'Talent Hub Stage': '4',
            'Orlov Room': '5',
            'Hope Room': '6',
            'Florentine Room': '7',
            'Shah Room': '8'
        };
        return stageMap[stageName] || '1';
    }

    getStageColor(stageNum) {
        const colors = {
            '1': '#102649',
            '2': '#F7C100',
            '3': '#0779B5',
            '4': '#CD5628',
            '5': '#004A87',
            '6': '#66883E',
            '7': '#981F1F',
            '8': '#FA8628'
        };
        return colors[stageNum] || '#102649';
    }

    formatTime(timeStr) {
        return timeStr; // Already formatted properly
    }

    getEventDuration(timeStr) {
        if (timeStr.includes(' - ')) {
            const [start, end] = timeStr.split(' - ');
            const startTime = this.parseTime(start);
            const endTime = this.parseTime(end);
            const duration = (endTime - startTime) / (1000 * 60);

            if (duration >= 60) {
                const hours = Math.floor(duration / 60);
                const mins = duration % 60;
                return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
            } else {
                return `${duration}m`;
            }
        }
        return '';
    }

    parseTime(timeStr) {
        const [time, period] = timeStr.split(' ');
        const [hours, minutes] = time.split(':').map(Number);
        let hour24 = hours;

        if (period === 'PM' && hours !== 12) {
            hour24 += 12;
        } else if (period === 'AM' && hours === 12) {
            hour24 = 0;
        }

        const date = new Date();
        date.setHours(hour24, minutes, 0, 0);
        return date;
    }

    isEventHappeningNow(timeStr) {
        if (!timeStr.includes(' - ')) return false;

        const [startStr, endStr] = timeStr.split(' - ');
        const startTime = this.parseTime(startStr);
        const endTime = this.parseTime(endStr);
        const now = new Date();

        return now >= startTime && now <= endTime;
    }

    isEventUpNext(timeStr) {
        const startTime = this.parseTime(timeStr.split(' - ')[0]);
        const now = new Date();
        const timeDiff = startTime - now;

        return timeDiff > 0 && timeDiff <= 30 * 60 * 1000;
    }

    updateCurrentTimeIndicator() {
        this.currentTime = new Date();

        let indicator = document.querySelector('.current-time-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'current-time-indicator';

            const mobileSchedule = document.querySelector('.mobile-schedule');
            if (mobileSchedule) {
                const nav = mobileSchedule.querySelector('.mobile-nav');
                if (nav) {
                    nav.appendChild(indicator);
                }
            }
        }

        const timeStr = this.currentTime.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });
        indicator.textContent = `Current Time: ${timeStr}`;

        this.updateEventStatuses();
    }

    updateEventStatuses() {
        const timelineEvents = document.querySelectorAll('.timeline-event');
        timelineEvents.forEach(event => {
            const parentSlot = event.closest('.timeline-slot');
            const timeHeader = parentSlot?.querySelector('.timeline-time');
            if (timeHeader) {
                const timeStr = timeHeader.textContent;
                event.classList.remove('happening-now', 'up-next');

                if (this.isEventHappeningNow(timeStr)) {
                    event.classList.add('happening-now');
                } else if (this.isEventUpNext(timeStr)) {
                    event.classList.add('up-next');
                }
            }
        });

        const stageEvents = document.querySelectorAll('.mobile-event');
        stageEvents.forEach(event => {
            const timeElement = event.querySelector('.mobile-event-time');
            const timeStr = timeElement ? timeElement.textContent : '';
            event.classList.remove('happening-now', 'up-next');

            if (this.isEventHappeningNow(timeStr)) {
                event.classList.add('happening-now');
            } else if (this.isEventUpNext(timeStr)) {
                event.classList.add('up-next');
            }
        });
    }

    setupSwipeGestures() {
        let startY = 0;
        let startX = 0;

        document.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', (e) => {
            if (!startY || !startX) return;

            const endY = e.changedTouches[0].clientY;
            const endX = e.changedTouches[0].clientX;
            const diffY = startY - endY;
            const diffX = startX - endX;

            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    this.switchToNextView();
                } else {
                    this.switchToPreviousView();
                }
            }

            startY = 0;
            startX = 0;
        });
    }

    switchToNextView() {
        if (this.currentView === 'timeline') {
            this.switchView('stage');
        }
    }

    switchToPreviousView() {
        if (this.currentView === 'stage') {
            this.switchView('timeline');
        }
    }

    setupQuickActions() {
        const quickActions = document.createElement('div');
        quickActions.className = 'mobile-quick-actions';
        quickActions.innerHTML = `
            <button class="quick-action-btn" id="scrollToNow" title="Go to current time">🕐</button>
            <button class="quick-action-btn" id="toggleView" title="Switch view">🔄</button>
        `;

        document.body.appendChild(quickActions);

        document.getElementById('scrollToNow').addEventListener('click', () => {
            this.scrollToCurrentTime();
        });

        document.getElementById('toggleView').addEventListener('click', () => {
            const newView = this.currentView === 'timeline' ? 'stage' : 'timeline';
            this.switchView(newView);
        });
    }

    scrollToCurrentTime() {
        const now = new Date();
        if (this.currentView === 'timeline') {
            const timeSlots = document.querySelectorAll('.timeline-time');
            let closestSlot = null;
            let minDiff = Infinity;

            timeSlots.forEach(slot => {
                const slotTime = this.parseTime(slot.textContent);
                const diff = Math.abs(now - slotTime);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestSlot = slot;
                }
            });

            if (closestSlot) {
                closestSlot.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            const stageView = document.querySelector('.mobile-stage-view');
            if (stageView) {
                stageView.scrollIntoView({ behavior: 'smooth', block: 'start' });
            }
        }
    }

    showEventDetails(eventData) {
        if (window.innerWidth <= 768) return;

        if (typeof showEventDetails === 'function') {
            const speakers = eventData.speakers.map(s => s.name).join(', ');
            showEventDetails(eventData.title, speakers, eventData.stage, eventData.time, eventData.description);
        }
    }
}

// Desktop time filter buttons

document.querySelectorAll('.filter-btn').forEach(button => {
    button.addEventListener('click', function () {
        const time = this.getAttribute('data-time');

        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');

        const allCells = document.querySelectorAll('td[data-time]');
        const allHeaders = document.querySelectorAll('th[data-time]');

        if (time === 'all') {
            allCells.forEach(cell => cell.style.display = 'table-cell');
            allHeaders.forEach(header => header.style.display = 'table-cell');
        } else {
            allCells.forEach(cell => {
                cell.style.display = cell.getAttribute('data-time') === time ? 'table-cell' : 'none';
            });
            allHeaders.forEach(header => {
                header.style.display = header.getAttribute('data-time') === time ? 'table-cell' : 'none';
            });
        }
    });
});

// Modal logic (desktop only)
const modal = document.getElementById('eventModal');
function showEventDetails(title, presenter, stage, time, description) {
    if (window.innerWidth <= 768) return;

    document.getElementById('modal-title').textContent = title;
    document.getElementById('modal-presenter').textContent = presenter;
    document.getElementById('modal-stage').textContent = stage;
    document.getElementById('modal-time').textContent = time;
    document.getElementById('modal-description').textContent = description;
    modal.style.display = 'flex';
}
function closeModal() {
    modal.style.display = 'none';
}
window.addEventListener('click', function (event) {
    if (event.target === modal) closeModal();
});

function scrollToCurrentTime() {
    const now = new Date();
    const totalMinutes = now.getHours() * 60 + now.getMinutes();
    let timeString;
    if (totalMinutes < 8 * 60) {
        timeString = '8-00';
    } else if (totalMinutes >= 16 * 60) {
        timeString = '15-45';
    } else {
        let roundedMinutes = Math.floor((totalMinutes - 8 * 60) / 15) * 15 + 8 * 60;
        let roundedHours = Math.floor(roundedMinutes / 60);
        let remainingMinutes = roundedMinutes % 60;
        timeString = `${roundedHours}-${remainingMinutes.toString().padStart(2, '0')}`;
    }

    const targetColumn = document.querySelector(`th[data-time="${timeString}"]`);
    if (targetColumn) targetColumn.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
}

document.addEventListener('DOMContentLoaded', function () {
    if (window.innerWidth <= 768) {
        window.mobileSchedule = new MobileSchedule();
    }
    const currentHour = new Date().getHours();
    if (currentHour >= 8 && currentHour < 16) {
        // Scroll to current time on desktop
        if (window.innerWidth > 768) {
            // A short delay to allow layout to settle
            // setTimeout(scrollToCurrentTime, 500);
        }
    }
});

window.addEventListener('resize', function () {
    if (window.innerWidth <= 768 && !window.mobileSchedule) {
        window.mobileSchedule = new MobileSchedule();
    }
});


document.addEventListener('DOMContentLoaded', function() {
    // Top Navigation Toggle
    const topMobileToggle = document.querySelector('.header .mobile-toggle');
    const topNavLinks = document.querySelector('.header .nav-links');

    if (topMobileToggle && topNavLinks) {
        topMobileToggle.addEventListener('click', function() {
            topNavLinks.classList.toggle('show');
            topMobileToggle.classList.toggle('active');
        });
    }

    // Bottom Navigation Toggle (if you have separate toggles)
    const bottomMobileToggle = document.querySelector('.nav-bar .mobile-toggle');
    const bottomNavLinks = document.querySelector('.nav-bar .nav-bar-links');
    const bottomNavActions = document.querySelector('.nav-bar .nav-bar-actions');

    if (bottomMobileToggle) {
        bottomMobileToggle.addEventListener('click', function() {
            if (bottomNavLinks) bottomNavLinks.classList.toggle('show');
            if (bottomNavActions) bottomNavActions.classList.toggle('show');
            bottomMobileToggle.classList.toggle('active');
        });
    }

    // Close mobile menus when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInsideNav = event.target.closest('.header') || event.target.closest('.nav-bar');

        if (!isClickInsideNav) {
            // Close all mobile menus
            const allMobileMenus = document.querySelectorAll('.nav-links.show, .nav-bar-links.show, .nav-bar-actions.show');
            const allToggleButtons = document.querySelectorAll('.mobile-toggle.active');

            allMobileMenus.forEach(menu => menu.classList.remove('show'));
            allToggleButtons.forEach(button => button.classList.remove('active'));
        }
    });

    // Close mobile menus on window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth >= 768) {
            const allMobileMenus = document.querySelectorAll('.nav-links.show, .nav-bar-links.show, .nav-bar-actions.show');
            const allToggleButtons = document.querySelectorAll('.mobile-toggle.active');

            allMobileMenus.forEach(menu => menu.classList.remove('show'));
            allToggleButtons.forEach(button => button.classList.remove('active'));
        }
    });


    const navToggle = document.querySelector('.nav-bar-toggle');
    const navLinks = document.querySelector('.nav-bar-links');
    const navActions = document.querySelector('.nav-bar-actions');
    const body = document.body;

    if (navToggle) {
        navToggle.addEventListener('click', function(e) {
            e.preventDefault();

            // Toggle active classes
            navToggle.classList.toggle('active');
            navLinks.classList.toggle('active');
            navActions.classList.toggle('active');
        });
    }

    // Close mobile menu when clicking on a link
    if (navLinks) {
        const navLinkItems = navLinks.querySelectorAll('a');
        navLinkItems.forEach(link => {
            link.addEventListener('click', function() {
                // Close menu after clicking a link
                navToggle.classList.remove('active');
                navLinks.classList.remove('active');
                navActions.classList.remove('active');
                body.classList.remove('nav-open');
            });
        });
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInsideNav = event.target.closest('.nav-bar');

        if (!isClickInsideNav && navToggle.classList.contains('active')) {
            navToggle.classList.remove('active');
            navLinks.classList.remove('active');
            navActions.classList.remove('active');
            body.classList.remove('nav-open');
        }
    });

    // Close mobile menu on window resize to desktop
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            navToggle.classList.remove('active');
            navLinks.classList.remove('active');
            navActions.classList.remove('active');
            body.classList.remove('nav-open');
        }
    });

    // Smooth scroll for anchor links
    const anchorLinks = navLinks.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);

            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    window.addEventListener('scroll', function() {
        const navBar = document.querySelector('.nav-bar');
        if (window.scrollY > 100) {
            navBar.style.position = 'sticky';
            navBar.style.top = '0';
            navBar.style.zIndex = '999';
        }
    });
});



