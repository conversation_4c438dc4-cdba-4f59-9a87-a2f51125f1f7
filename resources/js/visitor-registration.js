/**
 * Cairo Beacon Event - Visitor Registration
 * Main entry point for visitor registration functionality
 */

// Import components
import config from './components/config.js';
import utils from './components/utils.js';
import profilePhoto from './components/profilePhoto.js';
import passwordManager from './components/passwordManager.js';
import countryDropdown from './components/countryDropdown.js';
import modalManager from './components/modalManager.js';

// Main application object
const CairoBeaconEvent = {
    config,
    utils,
    profilePhoto,
    passwordManager,
    countryDropdown,
    modalManager,

    // Main initialization
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize all modules
            this.modalManager.initModal();
            this.profilePhoto.init();
            this.passwordManager.init();
            
            // Initialize cropper if needed
            if (typeof Cropper === 'undefined') {
                this.utils.loadScript(
                    'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js', 
                    () => this.profilePhoto.initCropper()
                );
            } else {
                this.profilePhoto.initCropper();
            }
        });
        
        // Set up Livewire event listeners
        document.addEventListener('livewire:initialized', () => {
            // Re-initialize password manager for Livewire components
            this.passwordManager.initializeToggles();

            // Set up global upload blocking prevention
            this.setupUploadBlockingPrevention();
        });

        // Handle Livewire navigation and updates
        document.addEventListener('livewire:navigated', () => {
            this.passwordManager.initializeToggles();
        });

        document.addEventListener('livewire:updated', () => {
            this.passwordManager.initializeToggles();
        });
    },

    /**
     * Set up global upload blocking prevention
     */
    setupUploadBlockingPrevention() {
        // Override Livewire's upload start behavior
        document.addEventListener('livewire:upload-start', (event) => {
            console.log('Global upload start handler - preventing UI blocking');

            // Immediately re-enable all form elements
            setTimeout(() => {
                this.forceEnableAllElements();
            }, 50);

            // Set up continuous monitoring
            const monitor = setInterval(() => {
                this.forceEnableAllElements();
            }, 200);

            // Store the monitor for cleanup
            window.uploadBlockingMonitor = monitor;
        });

        // Clean up on upload finish
        document.addEventListener('livewire:upload-finish', () => {
            console.log('Global upload finish handler');
            if (window.uploadBlockingMonitor) {
                clearInterval(window.uploadBlockingMonitor);
                window.uploadBlockingMonitor = null;
            }
            this.forceEnableAllElements();
        });

        // Clean up on upload error
        document.addEventListener('livewire:upload-error', () => {
            console.log('Global upload error handler');
            if (window.uploadBlockingMonitor) {
                clearInterval(window.uploadBlockingMonitor);
                window.uploadBlockingMonitor = null;
            }
            this.forceEnableAllElements();
        });
    },

    /**
     * Force enable all form elements
     */
    forceEnableAllElements() {
        // Enable all form elements
        document.querySelectorAll('input, button, select, textarea').forEach(el => {
            if (el.disabled) {
                el.disabled = false;
                el.removeAttribute('disabled');
            }
        });

        // Hide loading indicators
        document.querySelectorAll('[wire\\:loading]').forEach(el => {
            if (el.style.display !== 'none') {
                el.style.display = 'none';
            }
        });

        // Remove opacity classes
        document.querySelectorAll('.opacity-50, .opacity-25').forEach(el => {
            el.classList.remove('opacity-50', 'opacity-25');
        });

        // Restore pointer events
        document.querySelectorAll('[style*="pointer-events: none"]').forEach(el => {
            el.style.pointerEvents = 'auto';
        });

        // Remove any overlay elements that might be blocking
        document.querySelectorAll('.livewire-upload-overlay, .upload-overlay').forEach(el => {
            el.remove();
        });
    }
};

// Make Alpine.js components available globally
window.countryDropdown = CairoBeaconEvent.countryDropdown.create;

// Make necessary functions available globally for HTML onclick attributes
window.showImageCropper = CairoBeaconEvent.profilePhoto.showImageCropper.bind(CairoBeaconEvent.profilePhoto);
window.removeProfilePhoto = CairoBeaconEvent.profilePhoto.removeProfilePhoto.bind(CairoBeaconEvent.profilePhoto);
window.updateProfilePreview = CairoBeaconEvent.profilePhoto.updateProfilePreview.bind(CairoBeaconEvent.profilePhoto);
window.togglePasswordVisibility = CairoBeaconEvent.passwordManager.toggleVisibility.bind(CairoBeaconEvent.passwordManager);

// Initialize the application
CairoBeaconEvent.init();

// Export for use in other files if needed
export default CairoBeaconEvent;
