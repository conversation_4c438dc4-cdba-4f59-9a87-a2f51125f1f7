// Simple visitor name tag cropper - standalone version

// CRITICAL: Override the problematic function in profilePhoto.js
// This must run immediately to prevent the error

if (window.Livewire) {

    // Globally convert legacy 'profilePhoto' uploads to 'newPhoto' to avoid
    // hitting missing properties on parent components (e.g., payment-process)
    const originalUpload = window.Livewire.prototype.upload;

    window.Livewire.prototype.upload = function(name, file, successCallback, errorCallback) {
        console.log('🛑 Overriding problematic function in profilePhoto.js');
        if (name === 'profilePhoto') {
            console.warn('⚠️ Intercepted upload to "profilePhoto" – redirecting to "newPhoto"');
            name = 'newPhoto';
        }

        // Maintain original signature
        return originalUpload.call(this, name, file, successCallback, errorCallback);
    };
}

const visitorNameTagCropper = {
    cropper: null,
    croppedImageFile: null,
    croppedImageDataUrl: null,
    debugMode: true,
    isInitialized: false,

    /**
     * Simple utility functions
     */
    utils: {
        getElement: (selector) => document.querySelector(selector),
        /**
         * Resolve the correct Livewire component that owns the file input.
         * We start from #profilePhotoInput and walk *up* the DOM until we encounter
         * an element that has the `wire:id` attribute. This guarantees we grab the
         * innermost component (i.e. `visitor-name-tag`) instead of the outer
         * `payment-process`, which avoids the "Property [$profilePhoto] not found"
         * error.
         */
        getLivewireComponent: () => {
            // Direct by component id if available
            if (window.visitorNameTagComponentId && window.Livewire) {
                const cmp = window.Livewire.find(window.visitorNameTagComponentId);
                if (cmp) return cmp;
            }

            const input = document.querySelector('#profilePhotoInput');
            console.log('Visitor name tag cropper: Found file input:', input);
            if (!input || !window.Livewire) return null;

            // 1) Try nearest ancestor first (fast path)
            let el = input;
            while (el && !el.hasAttribute('wire:id')) {
                el = el.parentElement;
            }

            if (el && el.hasAttribute('wire:id')) {
                const cmp = window.Livewire.find(el.getAttribute('wire:id'));
                if (cmp && cmp.name === 'visitor-name-tag') {
                    return cmp;
                }
            }

            // 2) Scan every Livewire root and pick the one whose name is visitor-name-tag
            const allRoots = document.querySelectorAll('[wire\\:id]');
            for (const root of allRoots) {
                const cmp = window.Livewire.find(root.getAttribute('wire:id'));
                if (cmp && cmp.name === 'visitor-name-tag') {
                    return cmp;
                }
            }

            // 3) As a final fallback, return the first component that has the property 'newPhoto'
            for (const root of allRoots) {
                const cmp = window.Livewire.find(root.getAttribute('wire:id'));
                if (cmp && 'newPhoto' in cmp) {
                    return cmp;
                }
            }

            return null;
        }
    },

    /**
     * Initialize visitor name tag cropper functionality
     */
    init() {
        // Prevent double initialization
        if (this.isInitialized) {
            return;
        }

        // Only initialize if we're on the visitor name tag page and elements exist
        const photoInput = this.utils.getElement('#profilePhotoInput');
        const modal = this.utils.getElement('#imageCropperModal');

        if (!photoInput || !modal) {
            console.log('Visitor name tag elements not found, skipping initialization');
            return;
        }

        // Check if this is the visitor page by looking for specific elements
        const isVisitorPage = document.querySelector('.ticket-container') &&
            document.querySelector('.ticket-preview');

        if (!isVisitorPage) {
            console.log('Not on visitor page, skipping visitor cropper initialization');
            return;
        }

        // Make sure Cropper.js is available
        if (!window.Cropper) {
            console.error('Cropper.js not found - attempting to load it');
            // Try to load Cropper.js if not available
            const cropperScript = document.createElement('script');
            cropperScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js';
            document.head.appendChild(cropperScript);

            const cropperStyle = document.createElement('link');
            cropperStyle.rel = 'stylesheet';
            cropperStyle.href = 'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css';
            document.head.appendChild(cropperStyle);
        }

        console.log('Initializing visitor name tag cropper...');

        // EMERGENCY FIX: Clear ALL intervals that might be causing the infinite loop
        this.clearAllIntervals();

        // Completely disable profilePhoto.js if it's running
        if (window.profilePhoto) {
            // Disable debug mode to prevent logging
            window.profilePhoto.debugMode = false;

            // Completely override the problematic functions
            window.profilePhoto.uploadWithoutBlocking = function() {
                console.log('Profile photo upload disabled on visitor page');
                return false;
            };

            window.profilePhoto.forceEnableFormElements = function() {
                console.log('Force enable elements disabled on visitor page');
                return false;
            };

            console.log('Disabled profilePhoto.js for visitor page');
        }

        // Disable the global upload blocking monitor from visitor-registration.js
        if (window.uploadBlockingMonitor) {
            clearInterval(window.uploadBlockingMonitor);
            window.uploadBlockingMonitor = null;
            console.log('Cleared global upload blocking monitor');
        }

        // Override the global forceEnableAllElements function
        if (window.CairoBeaconEvent && window.CairoBeaconEvent.forceEnableAllElements) {
            window.CairoBeaconEvent.forceEnableAllElements = function() {
                console.log('Global force enable elements disabled on visitor page');
                return false;
            };
        }

        this.isInitialized = true;
        this.setupEventListeners();
        this.initLivewireHooks();
        console.log('Visitor name tag cropper initialized successfully');
    },

    /**
     * Setup all event listeners
     */
    setupEventListeners() {
        const photoInput = this.utils.getElement('#profilePhotoInput');

        if (photoInput) {
            // Remove any existing listeners first
            photoInput.removeEventListener('change', this.handleFileChange.bind(this));
            photoInput.addEventListener('change', this.handleFileChange.bind(this));
        }

        this.initCropper();
        this.initFileNameDisplay();
    },

    /**
     * Handle file input change
     */
    handleFileChange(event) {
        console.log('File selected for visitor name tag');
        this.showImageCropper(event);
    },

    /**
     * Show image cropper when photo is selected
     */
    showImageCropper(event) {
        if (!event.target.files || !event.target.files[0]) return;

        const file = event.target.files[0];

        // Validate file size (1MB limit)
        if (file.size > 1024 * 1024) {
            alert('The image must not exceed 1MB');
            event.target.value = '';
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file');
            event.target.value = '';
            return;
        }

        const reader = new FileReader();

        reader.onload = (e) => {
            const image = this.utils.getElement('#imageToCrop');
            if (!image) return;

            // Set image source and clear previous cropped data
            image.src = e.target.result;
            this.croppedImageDataUrl = null;
            this.croppedImageFile = null;

            const modalElement = this.utils.getElement('#imageCropperModal');
            if (modalElement && window.bootstrap) {
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                modal.show();

                image.onload = () => {
                    console.log('Visitor image loaded for cropping:', image.width, 'x', image.height);
                };
            }
        };

        reader.readAsDataURL(file);
    },

    /**
     * Update file name display
     */
    initFileNameDisplay() {
        const photoInput = this.utils.getElement('#profilePhotoInput');
        const fileNameDisplay = this.utils.getElement('#file-name-display');

        if (photoInput && fileNameDisplay) {
            photoInput.addEventListener('change', (event) => {
                if (event.target.files && event.target.files[0]) {
                    fileNameDisplay.textContent = event.target.files[0].name;
                } else {
                    fileNameDisplay.textContent = 'No File Chosen';
                }
            });
        }
    },

    /**
     * Remove visitor photo
     */
    removeVisitorPhoto(livewireComponent = null) {
        console.log('Removing visitor photo');

        const previewContainer = this.utils.getElement('#profilePreviewContainer');
        if (!previewContainer) return;

        previewContainer.innerHTML = `
            <div class="profile-placeholder d-flex align-items-center justify-content-center" id="profilePlaceholder">
                <i class="bi bi-person-fill fs-1 text-muted"></i>
            </div>
        `;

        const fileInput = this.utils.getElement('#profilePhotoInput');
        const fileNameDisplay = this.utils.getElement('#file-name-display');

        if (fileInput) fileInput.value = '';
        if (fileNameDisplay) fileNameDisplay.textContent = 'No File Chosen';

        this.croppedImageDataUrl = null;
        this.croppedImageFile = null;

        // Use passed livewire component or find it
        const component = livewireComponent || this.utils.getLivewireComponent();
        if (component) {
            component.set('newPhoto', null);
        }
    },

    /**
     * Update photo preview with cropped image
     */
    updatePhotoPreview(imageSrc) {
        const previewContainer = this.utils.getElement('#profilePreviewContainer');
        if (!previewContainer) return;

        const previewHTML = `
            <img src="${imageSrc}" alt="Profile Preview" class="img-thumbnail profile-preview" id="profilePreviewImage">
            <button type="button" class="btn btn-sm btn-danger edit-photo-btn" onclick="window.removeVisitorNameTagPhoto()">
                <i class="bi bi-trash"></i>
            </button>
        `;

        previewContainer.innerHTML = previewHTML;
    },

    /**
     * Initialize the image cropper
     */
    initCropper() {
        const modal = this.utils.getElement('#imageCropperModal');
        const cropButton = this.utils.getElement('#cropButton');

        if (!modal || !cropButton) return;

        // Add new listeners
        modal.addEventListener('shown.bs.modal', this.onModalShown.bind(this));
        modal.addEventListener('hidden.bs.modal', this.onModalHidden.bind(this));
        cropButton.addEventListener('click', this.onCropButtonClick.bind(this));
    },

    /**
     * Handle modal shown event
     */
    onModalShown() {
        const image = this.utils.getElement('#imageToCrop');
        if (!image) {
            console.error('Image element not found for cropping');
            return;
        }

        console.log('Modal shown, initializing cropper...');

        // Give a bit more time for the image to fully load
        setTimeout(() => {
            if (!window.Cropper) {
                console.error('Cropper.js library not loaded - attempting to load it');
                // Try to load Cropper.js if not available
                const cropperScript = document.createElement('script');
                cropperScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js';
                cropperScript.onload = () => this.initializeCropperInstance(image);
                document.head.appendChild(cropperScript);

                const cropperStyle = document.createElement('link');
                cropperStyle.rel = 'stylesheet';
                cropperStyle.href = 'https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css';
                document.head.appendChild(cropperStyle);
                return;
            }

            this.initializeCropperInstance(image);
        }, 500);
    },

    /**
     * Initialize the cropper instance
     */
    initializeCropperInstance(image) {
        if (image.naturalWidth === 0 || image.naturalHeight === 0) {
            console.error('Image not fully loaded - no dimensions available');
            // Try again in a moment
            setTimeout(() => this.initializeCropperInstance(image), 300);
            return;
        }

        try {
            // Destroy previous instance if it exists
            if (this.cropper) {
                this.cropper.destroy();
                this.cropper = null;
            }

            // Create new cropper instance
            const cropperOptions = {
                aspectRatio: 1,
                viewMode: 1,
                dragMode: 'move',
                autoCropArea: 1,
                restore: false,
                guides: true,
                center: true,
                highlight: false,
                cropBoxMovable: true,
                cropBoxResizable: true,
                toggleDragModeOnDblclick: false,
                minContainerWidth: 300,
                minContainerHeight: 300,
                minCropBoxWidth: 100,
                minCropBoxHeight: 100
            };

            this.cropper = new Cropper(image, cropperOptions);
            console.log('✅ Visitor name tag cropper initialized successfully');
        } catch (error) {
            console.error('Error initializing visitor name tag cropper:', error);
        }
    },

    /**
     * Handle modal hidden event
     */
    onModalHidden() {
        if (this.cropper) {
            this.cropper.destroy();
            this.cropper = null;
        }
        this.resetCropButton();
    },

    /**
     * Handle crop button click
     */
    onCropButtonClick() {
        if (!this.cropper) return;

        const cropButton = this.utils.getElement('#cropButton');
        if (cropButton) {
            cropButton.disabled = true;
            cropButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        }

        try {
            const canvas = this.cropper.getCroppedCanvas({
                width: 300,
                height: 300,
                fillColor: '#fff',
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high'
            });

            if (!canvas) {
                console.error('Failed to create cropped canvas');
                this.resetCropButton();
                return;
            }

            canvas.toBlob((blob) => {
                this.croppedImageFile = new File([blob], 'cropped-visitor-photo.png', { type: 'image/png' });

                const reader = new FileReader();
                reader.onloadend = () => {
                    this.croppedImageDataUrl = reader.result;
                    this.updatePhotoPreview(this.croppedImageDataUrl);
                };
                reader.readAsDataURL(blob);

                // Close modal
                const modal = this.utils.getElement('#imageCropperModal');
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }

                // Start upload immediately without blocking prevention
                this.simpleUpload();

            }, 'image/png');
        } catch (error) {
            console.error('Error during visitor image cropping:', error);
            this.resetCropButton();
        }
    },

    /**
     * Simple upload without the complex blocking prevention
     */
    simpleUpload() {
        // Use global clear function if available
        if (window.clearProblematicIntervals) {
            window.clearProblematicIntervals();
        } else if (this.clearAllIntervals) {
            this.clearAllIntervals();
        }

        const livewireComponent = this.utils.getLivewireComponent();
        if (livewireComponent && this.croppedImageFile) {
            // Skip any loading spinner visuals
            console.log('Starting simple upload of cropped visitor image');
            console.log('Livewire component name:', livewireComponent.name);
            console.log('Uploading to property: newPhoto');
            console.log('File size:', this.croppedImageFile.size, 'bytes');
            console.log('File type:', this.croppedImageFile.type);

            // Add error handling for overall Livewire errors
            const handleLivewireError = (event) => {
                console.error('Livewire error occurred during upload:', event.detail);
                this.showUploadError();
            };

            const handleUploadStart = () => {
                console.log('Upload started - tracking progress...');
            };

            const handleUploadProgress = (event) => {
                console.log(`Upload progress: ${event.detail.progress}%`);
            };

            const handleUploadFinish = () => {
                console.log('Visitor image upload completed successfully!');
                this.hideUploadProgress();

                // Remove all event listeners
                document.removeEventListener('livewire:upload-finish', handleUploadFinish);
                document.removeEventListener('livewire:upload-error', handleUploadError);
                document.removeEventListener('livewire:upload-start', handleUploadStart);
                document.removeEventListener('livewire:upload-progress', handleUploadProgress);
                document.removeEventListener('livewire:error', handleLivewireError);

                // Optional toast or inline success text (no reload needed)
                const successAlert = document.createElement('div');
                successAlert.className = 'alert alert-success';
                successAlert.innerHTML = 'Photo uploaded successfully!';
                document.querySelector('.ticket-container')?.prepend(successAlert);
                // Auto-hide after few seconds
                setTimeout(() => successAlert.remove(), 4000);
            };

            const handleUploadError = (event) => {
                console.error('Visitor image upload failed:', event.detail);

                // Remove all event listeners
                document.removeEventListener('livewire:upload-finish', handleUploadFinish);
                document.removeEventListener('livewire:upload-error', handleUploadError);
                document.removeEventListener('livewire:upload-start', handleUploadStart);
                document.removeEventListener('livewire:upload-progress', handleUploadProgress);
                document.removeEventListener('livewire:error', handleLivewireError);

                this.showUploadError();
            };

            // Set up all event listeners
            document.addEventListener('livewire:upload-start', handleUploadStart);
            document.addEventListener('livewire:upload-progress', handleUploadProgress);
            document.addEventListener('livewire:upload-finish', handleUploadFinish);
            document.addEventListener('livewire:upload-error', handleUploadError);
            document.addEventListener('livewire:error', handleLivewireError);

            // Assign cropped file to hidden Livewire file input (#photo-upload) so model updates
            const livewireFileInput = document.querySelector('#photo-upload');
            console.log('Livewire file input:', livewireFileInput);
            console.log('Assigning cropped file to Livewire file input:', livewireFileInput);
            if (livewireFileInput) {
                const dataTransfer = new DataTransfer();
                dataTransfer.items.add(this.croppedImageFile);
                livewireFileInput.files = dataTransfer.files;
                // Trigger change event so Livewire picks it up
                livewireFileInput.dispatchEvent(new Event('change'));
            }

            // Also call Livewire.upload to ensure model updates even if input change not detected
            if (livewireComponent) {
                console.log('Uploading via Livewire.upload as fallback');
                livewireComponent.upload('newPhoto', this.croppedImageFile, (uploadedFilename) => {
                    console.log('Livewire.upload success – filename:', uploadedFilename);
                }, (error) => {
                    console.error('Livewire.upload error:', error);
                });
            }

            console.log('Cropped file assigned to hidden Livewire input – fallback upload triggered');

        } else {
            console.error('Cannot upload visitor image: livewireComponent or croppedImageFile not available');
            if (!livewireComponent) {
                console.error('Livewire component not found');
            }
            if (!this.croppedImageFile) {
                console.error('No cropped image file to upload');
            }
            this.showUploadError();
        }
    },

    /**
     * Clear ALL intervals in the page - emergency fix for infinite loop
     */
    clearAllIntervals() {
        // Find the highest interval ID and clear all intervals
        const highestIntervalId = 500; // Arbitrary high number to catch most intervals
        for (let i = 0; i < highestIntervalId; i++) {
            window.clearInterval(i);
        }
        console.log(`Emergency fix: Cleared all intervals up to ID ${highestIntervalId}`);

        // Also clear specific known intervals
        if (window._profilePhotoIntervals) {
            window._profilePhotoIntervals.forEach(id => clearInterval(id));
            window._profilePhotoIntervals = [];
        }

        if (window.uploadBlockingMonitor) {
            clearInterval(window.uploadBlockingMonitor);
            window.uploadBlockingMonitor = null;
        }
    },

    /**
     * Disable spinner visuals per user request
     */
    showUploadProgress() {
        // No-op: we won't replace the preview with loading indicator
    },

    /**
     * Hide upload progress
     */
    hideUploadProgress() {
        const previewContainer = this.utils.getElement('#profilePreviewContainer');
        if (previewContainer && this.croppedImageDataUrl) {
            // Restore the cropped preview while we wait for the Livewire re-render
            this.updatePhotoPreview(this.croppedImageDataUrl);
        }
        this.resetCropButton();
        console.log('Upload progress hidden – restored preview image');
    },

    /**
     * Show upload error
     */
    showUploadError() {
        const previewContainer = this.utils.getElement('#profilePreviewContainer');
        if (previewContainer) {
            const errorHTML = `
                <div class="d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; background-color: #f8f9fa; border: 1px solid #dc3545; border-radius: 0.375rem;">
                    <div class="text-center">
                        <i class="bi bi-exclamation-triangle text-danger fs-3 mb-1"></i>
                        <div class="small text-danger">Upload failed</div>
                    </div>
                </div>
            `;
            previewContainer.innerHTML = errorHTML;
        }
    },

    /**
     * Reset crop button
     */
    resetCropButton() {
        const cropButton = this.utils.getElement('#cropButton');
        if (cropButton) {
            cropButton.disabled = false;
            cropButton.innerHTML = '<i class="bi bi-crop"></i> Crop & Save';
        }
    },

    /**
     * Listen for backend confirmation that image has been stored so we can swap
     * to the real stored URL (avoids caching issues)
     */
    initLivewireHooks() {
        document.addEventListener('visitor-photo-updated', (e) => {
            if (e.detail && e.detail.imageUrl) {
                this.croppedImageDataUrl = e.detail.imageUrl + '?v=' + Date.now();
                this.updatePhotoPreview(this.croppedImageDataUrl);
            }
        });
    }
};

// Make the remove function globally available for the onclick handler
window.removeVisitorNameTagPhoto = function(livewireComponent) {
    visitorNameTagCropper.removeVisitorPhoto(livewireComponent);
};

// Make the cropper globally available for debugging
window.visitorNameTagCropper = visitorNameTagCropper;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Add a delay to ensure other scripts have loaded
    setTimeout(() => {
        visitorNameTagCropper.init();
    }, 500);
});

// Also initialize on Livewire updates
document.addEventListener('livewire:updated', function() {
    setTimeout(() => {
        if (!visitorNameTagCropper.isInitialized) {
            visitorNameTagCropper.init();
        }
    }, 200);
});
