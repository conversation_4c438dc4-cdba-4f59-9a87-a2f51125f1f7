/**
 * Utility functions for Cairo Beacon Event
 */
const utils = {
    /**
     * Get DOM element by selector
     * @param {string} selector - CSS selector
     * @returns {HTMLElement|null} - DOM element or null if not found
     */
    getElement(selector) {
        return document.querySelector(selector);
    },

    /**
     * Load a script dynamically
     * @param {string} url - Script URL
     * @param {Function} callback - Callback function after script loads
     */
    loadScript(url, callback) {
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.src = url;
        script.onload = callback;
        document.head.appendChild(script);
    },

    /**
     * Find Livewire component ID
     * @returns {string|null} - Livewire component ID or null if not found
     */
    getLivewireId() {
        const element = document.querySelector('[wire\\:id]');
        return element ? element.getAttribute('wire:id') : null;
    },

    /**
     * Find Livewire component instance
     * @returns {Object|null} - Livewire component instance or null if not found
     */
    getLivewireComponent() {
        const id = this.getLivewireId();
        return id ? Livewire.find(id) : null;
    }
};

export default utils;
