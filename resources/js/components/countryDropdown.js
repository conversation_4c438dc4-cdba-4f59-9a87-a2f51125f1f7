/**
 * Country dropdown functionality
 */
import config from './config.js';

const countryDropdown = {
    /**
     * Create Alpine.js component for country dropdown
     * @returns {Object} - Alpine.js component definition
     */
    create() {
        return {
            isOpen: false,
            searchQuery: '',
            selectedCountryCode: '',
            selectedCallingCode: '',
            isWhatsApp: false,

            /**
             * Initialize country dropdown with default values
             * @param {string} countryCode - Initial country code
             * @param {string} callingCode - Initial calling code
             * @param {boolean} isWhatsApp - Whether this is for WhatsApp number
             */
            initCountryDropdown(countryCode, callingCode, isWhatsApp = false) {
                this.selectedCountryCode = countryCode || config.defaults.defaultCountryCode;
                this.selectedCallingCode = callingCode || config.defaults.defaultCallingCode;
                this.isWhatsApp = isWhatsApp;
                console.log('Initialized with:', this.selectedCountryCode, this.selectedCallingCode, this.isWhatsApp ? '(WhatsApp)' : '(Phone)');

                // Set up Livewire event listener
                document.addEventListener('livewire:initialized', () => {
                    // Listen for country updates from Livewire
                    Livewire.on('country-updated', data => {
                        if (!this.isWhatsApp) {
                            console.log('Received country-updated event:', data);
                            this.updateSelectedCountry(data.countryCode, data.callingCode);
                        }
                    });
                    
                    // Listen for WhatsApp country updates from Livewire
                    Livewire.on('whatsapp-country-updated', data => {
                        if (this.isWhatsApp) {
                            console.log('Received whatsapp-country-updated event:', data);
                            this.updateSelectedCountry(data.countryCode, data.callingCode);
                        }
                    });
                });
            },

            /**
             * Update selected country from Livewire events
             * @param {string} countryCode - Country code
             * @param {string} callingCode - Calling code
             */
            updateSelectedCountry(countryCode, callingCode) {
                console.log('Updating country to:', countryCode, callingCode);

                if (countryCode) {
                    this.selectedCountryCode = countryCode.toLowerCase();
                }
                if (callingCode) {
                    this.selectedCallingCode = callingCode;
                }
            },

            /**
             * Toggle dropdown visibility
             */
            toggleDropdown() {
                this.isOpen = !this.isOpen;
                if (this.isOpen) {
                    this.$nextTick(() => {
                        if (this.$refs.searchInput) {
                            this.$refs.searchInput.focus();
                        }
                    });
                }
            },

            /**
             * Select a country from the dropdown
             * @param {string} code - Country code
             * @param {string} callingCode - Calling code
             */
            selectCountry(code, callingCode) {
                console.log('Country selected:', code, callingCode, this.isWhatsApp ? '(WhatsApp)' : '(Phone)');

                // Determine which input to update based on whether this is for WhatsApp or phone
                const inputId = this.isWhatsApp ? 'whatsapp-country-hidden-input' : config.selectors.countryHiddenInput.substring(1);
                const countryInput = document.getElementById(inputId);

                if (countryInput) {
                    // Store the country code in uppercase as expected by the backend
                    const uppercaseCode = code.toUpperCase();
                    countryInput.value = uppercaseCode;

                    // Update local state for Alpine.js reactivity
                    this.selectedCountryCode = code.toLowerCase();
                    this.selectedCallingCode = callingCode;

                    // Trigger Livewire update for countryCode
                    countryInput.dispatchEvent(new Event('input', { bubbles: true }));

                    // Also update the appropriate property in Livewire
                    if (typeof Livewire !== 'undefined') {
                        const livewireComponent = Livewire.find(document.querySelector('[wire\\:id]').getAttribute('wire:id'));
                        if (this.isWhatsApp) {
                            livewireComponent.set('whatsappCountryCode', uppercaseCode);
                        } else {
                            livewireComponent.set('country', uppercaseCode);
                            livewireComponent.set('countryCode', uppercaseCode);
                        }
                    }

                    console.log('Updated hidden input with:', uppercaseCode);
                    console.log('Updated Alpine.js state:', this.selectedCountryCode, this.selectedCallingCode);
                } else {
                    console.error('Country input element not found:', inputId);
                }

                // Close the dropdown
                this.isOpen = false;
            },

            /**
             * Check if a country should be visible based on search query
             * @param {string} name - Country name
             * @param {string} code - Country code
             * @param {string} callingCode - Calling code
             * @returns {boolean} - Whether the country should be visible
             */
            isVisible(name, code, callingCode) {
                if (!this.searchQuery) return true;

                try {
                    const query = this.searchQuery.toLowerCase();
                    return (
                        name.toLowerCase().includes(query) ||
                        code.toLowerCase().includes(query) ||
                        callingCode.includes(query)
                    );
                } catch (error) {
                    console.error('Error in isVisible function:', error);
                    return true; // Default to showing the country if there's an error
                }
            },

            /**
             * Filter countries based on search query
             * (The filtering is handled directly in the template with x-show)
             */
            filterCountries() {}
        };
    }
};

export default countryDropdown;
