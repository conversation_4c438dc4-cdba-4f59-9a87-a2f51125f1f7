/**
 * Configuration settings for Cairo Beacon Event
 */
const config = {
    selectors: {
        // Profile photo related
        profilePhotoInput: '#profilePhotoInput',
        profilePreviewContainer: '#profilePreviewContainer',
        profilePlaceholder: '#profilePlaceholder',
        imageToCrop: '#imageToCrop',
        imageCropperModal: '#imageCropperModal',
        cropButton: '#cropButton',
        // Password related
        passwordToggle: '[data-toggle]',
        // Country dropdown related
        countryHiddenInput: '#country-hidden-input',
        whatsappCountryHiddenInput: '#whatsapp-country-hidden-input',
    },
    defaults: {
        defaultCountryCode: 'eg',
        defaultCallingCode: '20',
        cropperOptions: {
            aspectRatio: 1,
            viewMode: 1,
            dragMode: 'move',
            autoCropArea: 1,
            restore: false,
            guides: true,
            center: true,
            highlight: false,
            cropBoxMovable: true,
            cropBoxResizable: true,
            toggleDragModeOnDblclick: false
        },
        croppedImageSettings: {
            width: 300,
            height: 300,
            fillColor: '#fff',
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high'
        }
    }
};

export default config;
