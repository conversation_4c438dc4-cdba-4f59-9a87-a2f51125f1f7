/**
 * Modal functionality
 */
import config from './config.js';
import utils from './utils.js';

const modalManager = {
    /**
     * Initialize modal with proper focus management
     */
    initModal() {
        const modalElement = utils.getElement(
            config.selectors.imageCropperModal
        );
        
        if (!modalElement) return;
        
        // Fix for ARIA hidden issue - ensure proper focus management
        modalElement.addEventListener('hidden.bs.modal', () => {
            // When modal is hidden, return focus to the trigger element
            const profilePhotoInput = utils.getElement(
                config.selectors.profilePhotoInput
            );
            
            if (profilePhotoInput) {
                profilePhotoInput.focus();
            }
        });
        
        // Trap focus inside modal when open
        modalElement.addEventListener('shown.bs.modal', () => {
            // Focus the first focusable element in the modal
            const firstFocusable = modalElement.querySelector(
                'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            if (firstFocusable) {
                firstFocusable.focus();
            }
        });
    }
};

export default modalManager;
