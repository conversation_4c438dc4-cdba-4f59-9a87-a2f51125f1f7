/**
 * MINIMAL Emergency fix for infinite loop issue in visitor name tag page
 * This script ON<PERSON><PERSON> blocks problematic intervals while preserving ALL cropper functionality
 * Compatible with PaymentProcess component
 */

// MINIMAL FIX - Only target the specific cause of the infinite loop
(function() {
    console.log(' MINIMAL FIX: Applying visitor name tag infinite loop fix...');
    
    // Store original functions
    const originalSetInterval = window.setInterval;
    
    // Track problematic intervals
    window._problematicIntervals = [];
    
    // Function to clear only problematic intervals
    window.clearProblematicIntervals = function() {
        // Clear only specific problematic intervals
        if (window._problematicIntervals && window._problematicIntervals.length) {
            window._problematicIntervals.forEach(id => clearInterval(id));
            window._problematicIntervals = [];
        }
        
        // Clear specific known intervals
        if (window._profilePhotoIntervals && window._profilePhotoIntervals.length) {
            window._profilePhotoIntervals.forEach(id => clearInterval(id));
            window._profilePhotoIntervals = [];
        }
        
        if (window.uploadBlockingMonitor) {
            clearInterval(window.uploadBlockingMonitor);
            window.uploadBlockingMonitor = null;
        }
        
        return true;
    };
    
    // Override setInterval ONLY for problematic functions
    window.setInterval = function(callback, delay) {
        // Check if this is specifically the problematic interval
        const isProblematicInterval = typeof callback === 'function' && (
            callback.toString().includes('forceEnableFormElements') || 
            callback.toString().includes('forceEnable')
        );
        
        if (isProblematicInterval) {
            return -1; // Return a fake interval ID
        }
        
        // For all other intervals, create them normally
        const intervalId = originalSetInterval.apply(this, arguments);
        return intervalId;
    };
    
    // Only disable the specific problematic functions
    function disableProblematicFunctions() {
        // Disable only the problematic parts of profilePhoto.js
        if (window.profilePhoto) {
            window.profilePhoto.debugMode = false;
            
            // Only override the specific functions causing the infinite loop
            window.profilePhoto.forceEnableFormElements = function() { 
                return false; 
            };
            
            window.profilePhoto.uploadWithoutBlocking = function() { 
                return false; 
            };
            
            // Disable spinner functions entirely
            window.profilePhoto.showUploadProgress = function() { return false; };
            window.profilePhoto.hideUploadProgress = function() { return false; };
        }
        
        // Disable only problematic CairoBeaconEvent functionality
        if (window.CairoBeaconEvent) {
            if (window.CairoBeaconEvent.forceEnableAllElements) {
                window.CairoBeaconEvent.forceEnableAllElements = function() { 
                    return false; 
                };
            }
            
            if (window.CairoBeaconEvent.setupUploadBlockingPrevention) {
                window.CairoBeaconEvent.setupUploadBlockingPrevention = function() { 
                    return false; 
                };
            }
        }
    }
    
    // Call immediately
    disableProblematicFunctions();
    window.clearProblematicIntervals();
    
    // Set up event listeners for critical moments
    document.addEventListener('shown.bs.modal', function(e) {
        if (e.target && e.target.id === 'imageCropperModal') {
            window.clearProblematicIntervals();
        }
    });
    
    // Handle file input change events
    document.addEventListener('change', function(e) {
        if (e.target && e.target.id === 'profilePhotoInput') {
            window.clearProblematicIntervals();
        }
    });
    
    // Handle Livewire upload events
    document.addEventListener('livewire:upload-start', function() {
        window.clearProblematicIntervals();
    });
    
    document.addEventListener('livewire:upload-finish', function() {
        window.clearProblematicIntervals();
    });
    
    document.addEventListener('livewire:upload-error', function() {
        window.clearProblematicIntervals();
    });
    
    // Create a minimal watchdog that only runs occasionally
    const safeWatchdog = originalSetInterval(function() {
        window.clearProblematicIntervals();
    }, 10000); // Check every 10 seconds
    
    console.log(' MINIMAL FIX APPLIED: Visitor name tag infinite loop fix complete');
})();
