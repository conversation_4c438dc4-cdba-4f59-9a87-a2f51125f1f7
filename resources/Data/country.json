[{"name": "Afghanistan", "code": "AF", "nationality": "Afghan", "flag": "fi fi-af", "calling_code": "93"}, {"name": "Albania", "code": "AL", "nationality": "Albanian", "flag": "fi fi-al", "calling_code": "355"}, {"name": "Algeria", "code": "DZ", "nationality": "Algerian", "flag": "fi fi-dz", "calling_code": "213"}, {"name": "American Samoa", "code": "AS", "nationality": "American Samoan", "flag": "fi fi-as", "calling_code": "1684"}, {"name": "Andorra", "code": "AD", "nationality": "Andorran", "flag": "fi fi-ad", "calling_code": "376"}, {"name": "Angola", "code": "AO", "nationality": "Angolan", "flag": "fi fi-ao", "calling_code": "244"}, {"name": "<PERSON><PERSON><PERSON>", "code": "AI", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-ai", "calling_code": "1264"}, {"name": "Antarctica", "code": "AQ", "nationality": "Antarctic", "flag": "fi fi-aq", "calling_code": "672"}, {"name": "Antigua and Barbuda", "code": "AG", "nationality": "Antiguan and Barbudan", "flag": "fi fi-ag", "calling_code": "1268"}, {"name": "Argentina", "code": "AR", "nationality": "Argentine", "flag": "fi fi-ar", "calling_code": "54"}, {"name": "Armenia", "code": "AM", "nationality": "Armenian", "flag": "fi fi-am", "calling_code": "374"}, {"name": "Aruba", "code": "AW", "nationality": "Aruban", "flag": "fi fi-aw", "calling_code": "297"}, {"name": "Asia/Pacific Region", "code": "AP", "nationality": "Asia/Pacific Regional", "flag": "fi fi-ap", "calling_code": ""}, {"name": "Australia", "code": "AU", "nationality": "Australian", "flag": "fi fi-au", "calling_code": "61"}, {"name": "Austria", "code": "AT", "nationality": "Austrian", "flag": "fi fi-at", "calling_code": "43"}, {"name": "Azerbaijan", "code": "AZ", "nationality": "Azerbaijani", "flag": "fi fi-az", "calling_code": "994"}, {"name": "Bahamas", "code": "BS", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-bs", "calling_code": "1242"}, {"name": "Bahrain", "code": "BH", "nationality": "Bahraini", "flag": "fi fi-bh", "calling_code": "973"}, {"name": "Bangladesh", "code": "BD", "nationality": "Bangladeshi", "flag": "fi fi-bd", "calling_code": "880"}, {"name": "Barbados", "code": "BB", "nationality": "Barbadian", "flag": "fi fi-bb", "calling_code": "1246"}, {"name": "Belarus", "code": "BY", "nationality": "Belarusian", "flag": "fi fi-by", "calling_code": "375"}, {"name": "Belgium", "code": "BE", "nationality": "Belgian", "flag": "fi fi-be", "calling_code": "32"}, {"name": "Belize", "code": "BZ", "nationality": "Belizean", "flag": "fi fi-bz", "calling_code": "501"}, {"name": "Benin", "code": "BJ", "nationality": "Beninese", "flag": "fi fi-bj", "calling_code": "229"}, {"name": "Bermuda", "code": "BM", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "flag": "fi fi-bm", "calling_code": "1441"}, {"name": "Bhutan", "code": "BT", "nationality": "Bhutanese", "flag": "fi fi-bt", "calling_code": "975"}, {"name": "Bolivia", "code": "BO", "nationality": "Bolivian", "flag": "fi fi-bo", "calling_code": "591"}, {"name": "Bonaire, Sint Eustatius and Saba", "code": "BQ", "nationality": "Dutch", "flag": "fi fi-bq", "calling_code": ""}, {"name": "Bosnia and Herzegovina", "code": "BA", "nationality": "Bosnian and Herzegovinian", "flag": "fi fi-ba", "calling_code": "387"}, {"name": "Botswana", "code": "BW", "nationality": "Motswana", "flag": "fi fi-bw", "calling_code": "267"}, {"name": "Bouvet Island", "code": "BV", "nationality": "Bouvet Island", "flag": "fi fi-bv", "calling_code": ""}, {"name": "Brazil", "code": "BR", "nationality": "Brazilian", "flag": "fi fi-br", "calling_code": "55"}, {"name": "British Indian Ocean Territory", "code": "IO", "nationality": "British Indian Ocean Territory", "flag": "fi fi-io", "calling_code": "246"}, {"name": "Brunei Darussalam", "code": "BN", "nationality": "Bruneian", "flag": "fi fi-bn", "calling_code": "673"}, {"name": "Bulgaria", "code": "BG", "nationality": "Bulgarian", "flag": "fi fi-bg", "calling_code": "359"}, {"name": "Burkina Faso", "code": "BF", "nationality": "Burkinabé", "flag": "fi fi-bf", "calling_code": "226"}, {"name": "Burundi", "code": "BI", "nationality": "Burundian", "flag": "fi fi-bi", "calling_code": "257"}, {"name": "Cambodia", "code": "KH", "nationality": "Cambodian", "flag": "fi fi-kh", "calling_code": "855"}, {"name": "Cameroon", "code": "CM", "nationality": "Cameroonian", "flag": "fi fi-cm", "calling_code": "237"}, {"name": "Canada", "code": "CA", "nationality": "Canadian", "flag": "fi fi-ca", "calling_code": "1"}, {"name": "Cape Verde", "code": "CV", "nationality": "Cape Verdean", "flag": "fi fi-cv", "calling_code": "238"}, {"name": "Cayman Islands", "code": "KY", "nationality": "Caymanian", "flag": "fi fi-ky", "calling_code": "1345"}, {"name": "Central African Republic", "code": "CF", "nationality": "Central African", "flag": "fi fi-cf", "calling_code": "236"}, {"name": "Chad", "code": "TD", "nationality": "Chadian", "flag": "fi fi-td", "calling_code": "235"}, {"name": "Chile", "code": "CL", "nationality": "Chilean", "flag": "fi fi-cl", "calling_code": "56"}, {"name": "China", "code": "CN", "nationality": "Chinese", "flag": "fi fi-cn", "calling_code": "86"}, {"name": "Christmas Island", "code": "CX", "nationality": "Christmas Island", "flag": "fi fi-cx", "calling_code": "61"}, {"name": "Cocos (Keeling) Islands", "code": "CC", "nationality": "Cocos (Keeling) Islander", "flag": "fi fi-cc", "calling_code": "61"}, {"name": "Colombia", "code": "CO", "nationality": "Colombian", "flag": "fi fi-co", "calling_code": "57"}, {"name": "Comoros", "code": "KM", "nationality": "<PERSON><PERSON>", "flag": "fi fi-km", "calling_code": "269"}, {"name": "Congo", "code": "CG", "nationality": "Congolese", "flag": "fi fi-cg", "calling_code": "242"}, {"name": "Congo, The Democratic Republic of the", "code": "CD", "nationality": "Congolese", "flag": "fi fi-cd", "calling_code": "243"}, {"name": "Cook Islands", "code": "CK", "nationality": "Cook Islander", "flag": "fi fi-ck", "calling_code": "682"}, {"name": "Costa Rica", "code": "CR", "nationality": "Costa Rican", "flag": "fi fi-cr", "calling_code": "506"}, {"name": "Croatia", "code": "HR", "nationality": "Croatian", "flag": "fi fi-hr", "calling_code": "385"}, {"name": "Cuba", "code": "CU", "nationality": "Cuban", "flag": "fi fi-cu", "calling_code": "53"}, {"name": "Curaçao", "code": "CW", "nationality": "Curaçaoan", "flag": "fi fi-cw", "calling_code": ""}, {"name": "Cyprus", "code": "CY", "nationality": "Cypriot", "flag": "fi fi-cy", "calling_code": "357"}, {"name": "Czech Republic", "code": "CZ", "nationality": "Czech", "flag": "fi fi-cz", "calling_code": "420"}, {"name": "Côte d'Ivoire", "code": "CI", "nationality": "Ivorian", "flag": "fi fi-ci", "calling_code": "225"}, {"name": "Denmark", "code": "DK", "nationality": "Danish", "flag": "fi fi-dk", "calling_code": "45"}, {"name": "Djibouti", "code": "DJ", "nationality": "Djiboutian", "flag": "fi fi-dj", "calling_code": "253"}, {"name": "Dominica", "code": "DM", "nationality": "Dominican", "flag": "fi fi-dm", "calling_code": "1767"}, {"name": "Dominican Republic", "code": "DO", "nationality": "Dominican", "flag": "fi fi-do", "calling_code": "1809"}, {"name": "Ecuador", "code": "EC", "nationality": "Ecuadorian", "flag": "fi fi-ec", "calling_code": "593"}, {"name": "Egypt", "code": "EG", "nationality": "Egyptian", "flag": "fi fi-eg", "calling_code": "20"}, {"name": "El Salvador", "code": "SV", "nationality": "Salvadoran", "flag": "fi fi-sv", "calling_code": "503"}, {"name": "Equatorial Guinea", "code": "GQ", "nationality": "Equatorial Guinean", "flag": "fi fi-gq", "calling_code": "240"}, {"name": "Eritrea", "code": "ER", "nationality": "Eritrean", "flag": "fi fi-er", "calling_code": "291"}, {"name": "Estonia", "code": "EE", "nationality": "Estonian", "flag": "fi fi-ee", "calling_code": "372"}, {"name": "Ethiopia", "code": "ET", "nationality": "Ethiopian", "flag": "fi fi-et", "calling_code": "251"}, {"name": "Falkland Islands (Malvinas)", "code": "FK", "nationality": "Falkland Islander", "flag": "fi fi-fk", "calling_code": "500"}, {"name": "Faroe Islands", "code": "FO", "nationality": "Faroese", "flag": "fi fi-fo", "calling_code": "298"}, {"name": "Fiji", "code": "FJ", "nationality": "Fijian", "flag": "fi fi-fj", "calling_code": "679"}, {"name": "Finland", "code": "FI", "nationality": "Finnish", "flag": "fi fi-fi", "calling_code": "358"}, {"name": "France", "code": "FR", "nationality": "French", "flag": "fi fi-fr", "calling_code": "33"}, {"name": "French Guiana", "code": "GF", "nationality": "French Guianese", "flag": "fi fi-gf", "calling_code": "594"}, {"name": "French Polynesia", "code": "PF", "nationality": "French Polynesian", "flag": "fi fi-pf", "calling_code": "689"}, {"name": "French Southern Territories", "code": "TF", "nationality": "French Southern Territories", "flag": "fi fi-tf", "calling_code": ""}, {"name": "Gabon", "code": "GA", "nationality": "Gabonese", "flag": "fi fi-ga", "calling_code": "241"}, {"name": "Gambia", "code": "GM", "nationality": "Gambian", "flag": "fi fi-gm", "calling_code": "220"}, {"name": "Georgia", "code": "GE", "nationality": "Georgian", "flag": "fi fi-ge", "calling_code": "995"}, {"name": "Germany", "code": "DE", "nationality": "German", "flag": "fi fi-de", "calling_code": "49"}, {"name": "Ghana", "code": "GH", "nationality": "Ghanaian", "flag": "fi fi-gh", "calling_code": "233"}, {"name": "Gibraltar", "code": "GI", "nationality": "Gibraltarian", "flag": "fi fi-gi", "calling_code": "350"}, {"name": "Greece", "code": "GR", "nationality": "Greek", "flag": "fi fi-gr", "calling_code": "30"}, {"name": "Greenland", "code": "GL", "nationality": "Greenlandic", "flag": "fi fi-gl", "calling_code": "299"}, {"name": "Grenada", "code": "GD", "nationality": "Grenadian", "flag": "fi fi-gd", "calling_code": "1473"}, {"name": "Guadeloupe", "code": "GP", "nationality": "Guadeloupian", "flag": "fi fi-gp", "calling_code": "590"}, {"name": "Guam", "code": "GU", "nationality": "Guamanian", "flag": "fi fi-gu", "calling_code": "1671"}, {"name": "Guatemala", "code": "GT", "nationality": "Guatemalan", "flag": "fi fi-gt", "calling_code": "502"}, {"name": "Guernsey", "code": "GG", "nationality": "Channel Islander", "flag": "fi fi-gg", "calling_code": "44"}, {"name": "Guinea", "code": "GN", "nationality": "Guinean", "flag": "fi fi-gn", "calling_code": "224"}, {"name": "Guinea-Bissau", "code": "GW", "nationality": "Guinea-Bissauan", "flag": "fi fi-gw", "calling_code": "245"}, {"name": "Guyana", "code": "GY", "nationality": "Guyanese", "flag": "fi fi-gy", "calling_code": "592"}, {"name": "Haiti", "code": "HT", "nationality": "Haitian", "flag": "fi fi-ht", "calling_code": "509"}, {"name": "Heard Island and Mcdonald Islands", "code": "HM", "nationality": "Heard Island and McDonald Islands", "flag": "fi fi-hm", "calling_code": ""}, {"name": "Holy See (Vatican City State)", "code": "VA", "nationality": "Vatican", "flag": "fi fi-va", "calling_code": "39"}, {"name": "Honduras", "code": "HN", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-hn", "calling_code": "504"}, {"name": "Hong Kong", "code": "HK", "nationality": "<PERSON>", "flag": "fi fi-hk", "calling_code": "852"}, {"name": "Hungary", "code": "HU", "nationality": "Hungarian", "flag": "fi fi-hu", "calling_code": "36"}, {"name": "Iceland", "code": "IS", "nationality": "Icelandic", "flag": "fi fi-is", "calling_code": "354"}, {"name": "India", "code": "IN", "nationality": "Indian", "flag": "fi fi-in", "calling_code": "91"}, {"name": "Indonesia", "code": "ID", "nationality": "Indonesian", "flag": "fi fi-id", "calling_code": "62"}, {"name": "Iran, Islamic Republic Of", "code": "IR", "nationality": "Iranian", "flag": "fi fi-ir", "calling_code": "98"}, {"name": "Iraq", "code": "IQ", "nationality": "Iraqi", "flag": "fi fi-iq", "calling_code": "964"}, {"name": "Ireland", "code": "IE", "nationality": "Irish", "flag": "fi fi-ie", "calling_code": "353"}, {"name": "Isle of Man", "code": "IM", "nationality": "Manx", "flag": "fi fi-im", "calling_code": "44"}, {"name": "Israel", "code": "IL", "nationality": "Israeli", "flag": "fi fi-il", "calling_code": "972"}, {"name": "Italy", "code": "IT", "nationality": "Italian", "flag": "fi fi-it", "calling_code": "39"}, {"name": "Jamaica", "code": "JM", "nationality": "Jamaican", "flag": "fi fi-jm", "calling_code": "1876"}, {"name": "Japan", "code": "JP", "nationality": "Japanese", "flag": "fi fi-jp", "calling_code": "81"}, {"name": "Jersey", "code": "JE", "nationality": "Channel Islander", "flag": "fi fi-je", "calling_code": "44"}, {"name": "Jordan", "code": "JO", "nationality": "<PERSON><PERSON>", "flag": "fi fi-jo", "calling_code": "962"}, {"name": "Kazakhstan", "code": "KZ", "nationality": "Kazakhstani", "flag": "fi fi-kz", "calling_code": "7"}, {"name": "Kenya", "code": "KE", "nationality": "Kenyan", "flag": "fi fi-ke", "calling_code": "254"}, {"name": "Kiribati", "code": "KI", "nationality": "I-Kiribati", "flag": "fi fi-ki", "calling_code": "686"}, {"name": "Korea, Republic of", "code": "KR", "nationality": "Korean", "flag": "fi fi-kr", "calling_code": "82"}, {"name": "Kuwait", "code": "KW", "nationality": "Kuwaiti", "flag": "fi fi-kw", "calling_code": "965"}, {"name": "Kyrgyzstan", "code": "KG", "nationality": "Kyrgyz", "flag": "fi fi-kg", "calling_code": "996"}, {"name": "Laos", "code": "LA", "nationality": "Lao", "flag": "fi fi-la", "calling_code": "856"}, {"name": "Latvia", "code": "LV", "nationality": "Latvian", "flag": "fi fi-lv", "calling_code": "371"}, {"name": "Lebanon", "code": "LB", "nationality": "Lebanese", "flag": "fi fi-lb", "calling_code": "961"}, {"name": "Lesotho", "code": "LS", "nationality": "Mosoth<PERSON>", "flag": "fi fi-ls", "calling_code": "266"}, {"name": "Liberia", "code": "LR", "nationality": "Liberian", "flag": "fi fi-lr", "calling_code": "231"}, {"name": "Libyan Arab Jam<PERSON>riya", "code": "LY", "nationality": "Libyan", "flag": "fi fi-ly", "calling_code": "218"}, {"name": "Liechtenstein", "code": "LI", "nationality": "Liechtensteiner", "flag": "fi fi-li", "calling_code": "423"}, {"name": "Lithuania", "code": "LT", "nationality": "Lithuanian", "flag": "fi fi-lt", "calling_code": "370"}, {"name": "Luxembourg", "code": "LU", "nationality": "Luxembourgish", "flag": "fi fi-lu", "calling_code": "352"}, {"name": "Macao", "code": "MO", "nationality": "Macanese", "flag": "fi fi-mo", "calling_code": "853"}, {"name": "Madagascar", "code": "MG", "nationality": "Malagasy", "flag": "fi fi-mg", "calling_code": "261"}, {"name": "Malawi", "code": "MW", "nationality": "Malawian", "flag": "fi fi-mw", "calling_code": "265"}, {"name": "Malaysia", "code": "MY", "nationality": "Malaysian", "flag": "fi fi-my", "calling_code": "60"}, {"name": "Maldives", "code": "MV", "nationality": "Maldivian", "flag": "fi fi-mv", "calling_code": "960"}, {"name": "Mali", "code": "ML", "nationality": "<PERSON><PERSON>", "flag": "fi fi-ml", "calling_code": "223"}, {"name": "Malta", "code": "MT", "nationality": "Maltese", "flag": "fi fi-mt", "calling_code": "356"}, {"name": "Marshall Islands", "code": "MH", "nationality": "<PERSON><PERSON>", "flag": "fi fi-mh", "calling_code": "692"}, {"name": "Martinique", "code": "MQ", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "flag": "fi fi-mq", "calling_code": "596"}, {"name": "Mauritania", "code": "MR", "nationality": "Mauritanian", "flag": "fi fi-mr", "calling_code": "222"}, {"name": "Mauritius", "code": "MU", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-mu", "calling_code": "230"}, {"name": "Mayotte", "code": "YT", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-yt", "calling_code": "262"}, {"name": "Mexico", "code": "MX", "nationality": "Mexican", "flag": "fi fi-mx", "calling_code": "52"}, {"name": "Micronesia, Federated States of", "code": "FM", "nationality": "Micronesian", "flag": "fi fi-fm", "calling_code": "691"}, {"name": "Moldova, Republic of", "code": "MD", "nationality": "Moldovan", "flag": "fi fi-md", "calling_code": "373"}, {"name": "Monaco", "code": "MC", "nationality": "Monégasque", "flag": "fi fi-mc", "calling_code": "377"}, {"name": "Mongolia", "code": "MN", "nationality": "Mongolian", "flag": "fi fi-mn", "calling_code": "976"}, {"name": "Montenegro", "code": "ME", "nationality": "Montenegrin", "flag": "fi fi-me", "calling_code": "382"}, {"name": "Montserrat", "code": "MS", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "flag": "fi fi-ms", "calling_code": "1664"}, {"name": "Morocco", "code": "MA", "nationality": "Moroccan", "flag": "fi fi-ma", "calling_code": "212"}, {"name": "Mozambique", "code": "MZ", "nationality": "Mozambican", "flag": "fi fi-mz", "calling_code": "258"}, {"name": "Myanmar", "code": "MM", "nationality": "Burmese", "flag": "fi fi-mm", "calling_code": "95"}, {"name": "Namibia", "code": "NA", "nationality": "Namibian", "flag": "fi fi-na", "calling_code": "264"}, {"name": "Nauru", "code": "NR", "nationality": "Nauruan", "flag": "fi fi-nr", "calling_code": "674"}, {"name": "Nepal", "code": "NP", "nationality": "Nepali", "flag": "fi fi-np", "calling_code": "977"}, {"name": "Netherlands", "code": "NL", "nationality": "Dutch", "flag": "fi fi-nl", "calling_code": "31"}, {"name": "Netherlands Antilles", "code": "AN", "nationality": "Dutch Antillean", "flag": "fi fi-an", "calling_code": ""}, {"name": "New Caledonia", "code": "NC", "nationality": "New Caledonian", "flag": "fi fi-nc", "calling_code": "687"}, {"name": "New Zealand", "code": "NZ", "nationality": "New Zealander", "flag": "fi fi-nz", "calling_code": "64"}, {"name": "Nicaragua", "code": "NI", "nationality": "Nicaraguan", "flag": "fi fi-ni", "calling_code": "505"}, {"name": "Niger", "code": "NE", "nationality": "Nigerien", "flag": "fi fi-ne", "calling_code": "227"}, {"name": "Nigeria", "code": "NG", "nationality": "Nigerian", "flag": "fi fi-ng", "calling_code": "234"}, {"name": "Niue", "code": "NU", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-nu", "calling_code": "683"}, {"name": "Norfolk Island", "code": "NF", "nationality": "Norfolk Islander", "flag": "fi fi-nf", "calling_code": "672"}, {"name": "North Korea", "code": "KP", "nationality": "North Korean", "flag": "fi fi-kp", "calling_code": "850"}, {"name": "North Macedonia", "code": "MK", "nationality": "Macedonian", "flag": "fi fi-mk", "calling_code": "389"}, {"name": "Northern Mariana Islands", "code": "MP", "nationality": "Northern Mariana Islander", "flag": "fi fi-mp", "calling_code": "1670"}, {"name": "Norway", "code": "NO", "nationality": "Norwegian", "flag": "fi fi-no", "calling_code": "47"}, {"name": "Oman", "code": "OM", "nationality": "Omani", "flag": "fi fi-om", "calling_code": "968"}, {"name": "Pakistan", "code": "PK", "nationality": "Pakistani", "flag": "fi fi-pk", "calling_code": "92"}, {"name": "<PERSON><PERSON>", "code": "PW", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-pw", "calling_code": "680"}, {"name": "Palestinian Territory, Occupied", "code": "PS", "nationality": "Palestinian", "flag": "fi fi-ps", "calling_code": "970"}, {"name": "Panama", "code": "PA", "nationality": "Panamanian", "flag": "fi fi-pa", "calling_code": "507"}, {"name": "Papua New Guinea", "code": "PG", "nationality": "Papua New Guinean", "flag": "fi fi-pg", "calling_code": "675"}, {"name": "Paraguay", "code": "PY", "nationality": "Paraguayan", "flag": "fi fi-py", "calling_code": "595"}, {"name": "Peru", "code": "PE", "nationality": "Peruvian", "flag": "fi fi-pe", "calling_code": "51"}, {"name": "Philippines", "code": "PH", "nationality": "Filipino", "flag": "fi fi-ph", "calling_code": "63"}, {"name": "Pitcairn Islands", "code": "PN", "nationality": "Pitcairn Islander", "flag": "fi fi-pn", "calling_code": "64"}, {"name": "Poland", "code": "PL", "nationality": "Polish", "flag": "fi fi-pl", "calling_code": "48"}, {"name": "Portugal", "code": "PT", "nationality": "Portuguese", "flag": "fi fi-pt", "calling_code": "351"}, {"name": "Puerto Rico", "code": "PR", "nationality": "Puerto Rican", "flag": "fi fi-pr", "calling_code": "1787"}, {"name": "Qatar", "code": "QA", "nationality": "Qatari", "flag": "fi fi-qa", "calling_code": "974"}, {"name": "Reunion", "code": "RE", "nationality": "Réunionese", "flag": "fi fi-re", "calling_code": "262"}, {"name": "Romania", "code": "RO", "nationality": "Romanian", "flag": "fi fi-ro", "calling_code": "40"}, {"name": "Russian Federation", "code": "RU", "nationality": "Russian", "flag": "fi fi-ru", "calling_code": "7"}, {"name": "Rwanda", "code": "RW", "nationality": "Rwandan", "flag": "fi fi-rw", "calling_code": "250"}, {"name": "<PERSON>", "code": "BL", "nationality": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flag": "fi fi-bl", "calling_code": "590"}, {"name": "Saint Helena", "code": "SH", "nationality": "<PERSON> <PERSON>", "flag": "fi fi-sh", "calling_code": "290"}, {"name": "Saint Kitts and Nevis", "code": "KN", "nationality": "Kittitian or Nevisian", "flag": "fi fi-kn", "calling_code": "1869"}, {"name": "Saint Lucia", "code": "LC", "nationality": "Saint Lucian", "flag": "fi fi-lc", "calling_code": "1758"}, {"name": "Saint <PERSON>", "code": "MF", "nationality": "Saint-Martinoise", "flag": "fi fi-mf", "calling_code": "590"}, {"name": "Saint <PERSON>", "code": "MF", "nationality": "Saint-Martinoise", "flag": "fi fi-mf", "calling_code": "590"}, {"name": "Saint Pierre and Miquelon", "code": "PM", "nationality": "Saint-Pierrais or Miquelonnais", "flag": "fi fi-pm", "calling_code": "508"}, {"name": "Saint Vincent and the Grenadines", "code": "VC", "nationality": "<PERSON>", "flag": "fi fi-vc", "calling_code": "1784"}, {"name": "Samoa", "code": "WS", "nationality": "Samoan", "flag": "fi fi-ws", "calling_code": "685"}, {"name": "San Marino", "code": "SM", "nationality": "Sammarinese", "flag": "fi fi-sm", "calling_code": "378"}, {"name": "Sao Tome and Principe", "code": "ST", "nationality": "<PERSON>", "flag": "fi fi-st", "calling_code": "239"}, {"name": "Saudi Arabia", "code": "SA", "nationality": "Saudi Arabian", "flag": "fi fi-sa", "calling_code": "966"}, {"name": "Senegal", "code": "SN", "nationality": "Senegalese", "flag": "fi fi-sn", "calling_code": "221"}, {"name": "Serbia", "code": "RS", "nationality": "Serbian", "flag": "fi fi-rs", "calling_code": "381"}, {"name": "Serbia and Montenegro", "code": "CS", "nationality": "Serbian and Montenegrin", "flag": "fi fi-cs", "calling_code": ""}, {"name": "Seychelles", "code": "SC", "nationality": "<PERSON><PERSON><PERSON><PERSON>", "flag": "fi fi-sc", "calling_code": "248"}, {"name": "Sierra Leone", "code": "SL", "nationality": "Sierra Leonean", "flag": "fi fi-sl", "calling_code": "232"}, {"name": "Singapore", "code": "SG", "nationality": "Singaporean", "flag": "fi fi-sg", "calling_code": "65"}, {"name": "Sint Maarten", "code": "SX", "nationality": "Sint Maartener", "flag": "fi fi-sx", "calling_code": "1721"}, {"name": "Slovakia", "code": "SK", "nationality": "Slovak", "flag": "fi fi-sk", "calling_code": "421"}, {"name": "Slovenia", "code": "SI", "nationality": "Slovenian", "flag": "fi fi-si", "calling_code": "386"}, {"name": "Solomon Islands", "code": "SB", "nationality": "Solomon Islander", "flag": "fi fi-sb", "calling_code": "677"}, {"name": "Somalia", "code": "SO", "nationality": "Somali", "flag": "fi fi-so", "calling_code": "252"}, {"name": "South Africa", "code": "ZA", "nationality": "South African", "flag": "fi fi-za", "calling_code": "27"}, {"name": "South Georgia and the South Sandwich Islands", "code": "GS", "nationality": "South Georgia and the South Sandwich Islands", "flag": "fi fi-gs", "calling_code": ""}, {"name": "South Sudan", "code": "SS", "nationality": "South Sudanese", "flag": "fi fi-ss", "calling_code": "211"}, {"name": "Spain", "code": "ES", "nationality": "Spanish", "flag": "fi fi-es", "calling_code": "34"}, {"name": "Sri Lanka", "code": "LK", "nationality": "Sri Lankan", "flag": "fi fi-lk", "calling_code": "94"}, {"name": "Sudan", "code": "SD", "nationality": "Sudanese", "flag": "fi fi-sd", "calling_code": "249"}, {"name": "Suriname", "code": "SR", "nationality": "Surinamese", "flag": "fi fi-sr", "calling_code": "597"}, {"name": "Svalbard and <PERSON>", "code": "SJ", "nationality": "Svalbard and <PERSON>", "flag": "fi fi-sj", "calling_code": "47"}, {"name": "Swaziland", "code": "SZ", "nationality": "Swazi", "flag": "fi fi-sz", "calling_code": "268"}, {"name": "Sweden", "code": "SE", "nationality": "Swedish", "flag": "fi fi-se", "calling_code": "46"}, {"name": "Switzerland", "code": "CH", "nationality": "Swiss", "flag": "fi fi-ch", "calling_code": "41"}, {"name": "Syrian Arab Republic", "code": "SY", "nationality": "Syrian", "flag": "fi fi-sy", "calling_code": "963"}, {"name": "Taiwan", "code": "TW", "nationality": "Taiwanese", "flag": "fi fi-tw", "calling_code": "886"}, {"name": "Tajikistan", "code": "TJ", "nationality": "Tajikistani", "flag": "fi fi-tj", "calling_code": "992"}, {"name": "Tanzania, United Republic of", "code": "TZ", "nationality": "Tanzanian", "flag": "fi fi-tz", "calling_code": "255"}, {"name": "Thailand", "code": "TH", "nationality": "Thai", "flag": "fi fi-th", "calling_code": "66"}, {"name": "Timor-Leste", "code": "TL", "nationality": "Timorese", "flag": "fi fi-tl", "calling_code": "670"}, {"name": "Togo", "code": "TG", "nationality": "Togolese", "flag": "fi fi-tg", "calling_code": "228"}, {"name": "Tokelau", "code": "TK", "nationality": "Tokelauan", "flag": "fi fi-tk", "calling_code": "690"}, {"name": "Tonga", "code": "TO", "nationality": "Tongan", "flag": "fi fi-to", "calling_code": "676"}, {"name": "Trinidad and Tobago", "code": "TT", "nationality": "Trinidadian or Tobagonian", "flag": "fi fi-tt", "calling_code": "1868"}, {"name": "Tunisia", "code": "TN", "nationality": "Tunisian", "flag": "fi fi-tn", "calling_code": "216"}, {"name": "Turkey", "code": "TR", "nationality": "Turkish", "flag": "fi fi-tr", "calling_code": "90"}, {"name": "Turkmenistan", "code": "TM", "nationality": "Turkmen", "flag": "fi fi-tm", "calling_code": "993"}, {"name": "Turks and Caicos Islands", "code": "TC", "nationality": "Turks and Caicos Islander", "flag": "fi fi-tc", "calling_code": "1649"}, {"name": "Tuvalu", "code": "TV", "nationality": "Tuvaluan", "flag": "fi fi-tv", "calling_code": "688"}, {"name": "Uganda", "code": "UG", "nationality": "Ugandan", "flag": "fi fi-ug", "calling_code": "256"}, {"name": "Ukraine", "code": "UA", "nationality": "Ukrainian", "flag": "fi fi-ua", "calling_code": "380"}, {"name": "United Arab Emirates", "code": "AE", "nationality": "Emirati", "flag": "fi fi-ae", "calling_code": "971"}, {"name": "United Kingdom", "code": "GB", "nationality": "British", "flag": "fi fi-gb", "calling_code": "44"}, {"name": "United States", "code": "US", "nationality": "American", "flag": "fi fi-us", "calling_code": "1"}, {"name": "United States Minor Outlying Islands", "code": "UM", "nationality": "American", "flag": "fi fi-um", "calling_code": ""}, {"name": "Uruguay", "code": "UY", "nationality": "Uruguayan", "flag": "fi fi-uy", "calling_code": "598"}, {"name": "Uzbekistan", "code": "UZ", "nationality": "Uzbekistani", "flag": "fi fi-uz", "calling_code": "998"}, {"name": "Vanuatu", "code": "VU", "nationality": "Ni-Vanuatu", "flag": "fi fi-vu", "calling_code": "678"}, {"name": "Venezuela", "code": "VE", "nationality": "Venezuelan", "flag": "fi fi-ve", "calling_code": "58"}, {"name": "Vietnam", "code": "VN", "nationality": "Vietnamese", "flag": "fi fi-vn", "calling_code": "84"}, {"name": "Virgin Islands, British", "code": "VG", "nationality": "British Virgin Islander", "flag": "fi fi-vg", "calling_code": "1284"}, {"name": "Virgin Islands, U.S.", "code": "VI", "nationality": "U.S. Virgin Islander", "flag": "fi fi-vi", "calling_code": "1340"}, {"name": "Wallis and Futuna", "code": "WF", "nationality": "Wallisian or Futunan", "flag": "fi fi-wf", "calling_code": "681"}, {"name": "Western Sahara", "code": "EH", "nationality": "<PERSON><PERSON><PERSON>", "flag": "fi fi-eh", "calling_code": "212"}, {"name": "Yemen", "code": "YE", "nationality": "Yemeni", "flag": "fi fi-ye", "calling_code": "967"}, {"name": "Zambia", "code": "ZM", "nationality": "Zambian", "flag": "fi fi-zm", "calling_code": "260"}, {"name": "Zimbabwe", "code": "ZW", "nationality": "Zimbabwean", "flag": "fi fi-zw", "calling_code": "263"}, {"name": "Åland Islands", "code": "AX", "nationality": "Ålandish", "flag": "fi fi-ax", "calling_code": ""}]