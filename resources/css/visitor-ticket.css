/* Custom Ticket Styles */
body, html, h1, h2, h3, h4, h5, h6, p, a, span, div, ul, ol, li, input, button, textarea, select, label, table, th, td, tr, form, nav, header, footer, section, article, aside, main {
    font-family: 'Gotham Narrow', Arial, sans-serif !important;
}

.ticket-wrapper {
    max-width: 800px;
    margin: 50px auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.ticket-card {
    width: 100%;
    background-color: white;
    border: 2px solid white;
    border-radius: 15px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    padding: 40px 30px;
    margin-bottom: 20px;
    position: relative;
    text-align: center;
}

.ticket-content {
    padding: 10px;
}

.ticket-title {
    margin-bottom: 25px;
}

.my-text {
    color: #007DB6;
    font-size: 28px;
    font-weight: 600;
    text-transform: uppercase;
    word-wrap: break-word;
}

.ticket-text {
    color: #333;
    font-size: 28px;
    font-weight: 600;
    text-transform: uppercase;
    word-wrap: break-word;
}

.ticket-message {
    margin-bottom: 20px;
}

.dear-text {
    color: #007DB6;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
}

.details-text {
    color: #007DB6;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    word-wrap: break-word;
}

.ticket-details {
    color: #555;
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
    margin-bottom: 12px;
}

.mb-4 {
    margin-bottom: 2rem;
}

.agenda-button-container {
    margin-top: 30px;
    text-align: center;
    width: 100%;
}

.agenda-button {
    display: inline-block;
    background-color: #102649;
    color: white;
    font-size: 16px;
    font-weight: 500;
    word-wrap: break-word;
    padding: 14px 40px;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.agenda-button:hover {
    background-color: #0a1a33;
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25);
}

.ticket-benefits {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 18px;
  align-items: flex-start;
  max-width: 410px;
  margin-left: auto;
  margin-right: auto;
}

.ticket-benefits li {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  border-radius: 10px;
  padding: 12px 18px 12px 14px;
  font-size: 18px;
  font-weight: 500;
  text-align: left;
  box-shadow: none;
  transition: background 0.2s;
  margin-bottom: 0;
  background: none;
}

.ticket-benefits li:hover {
  background: none;
}

.ticket-benefits svg {
  flex-shrink: 0;
  margin-top: 2px;
  width: 22px;
  height: 22px;
  background: #eaf6fd;
  border-radius: 50%;
  box-shadow: 0 0 0 2px #eaf6fd;
}

.ticket-benefits span {
  font-size: 18px;
  font-weight: 400;
  line-height: 1.45;
}

@media (max-width: 600px) {
  .ticket-benefits {
    max-width: 100%;
    padding: 0 8px;
  }
  .ticket-benefits li {
    font-size: 16px;
    padding: 10px 10px 10px 10px;
  }
  .ticket-benefits span {
    font-size: 16px;
  }
}

/* Ticket Payment Details Styles */
.payment-details-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: white;
    box-shadow: 0px 4px 24px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 24px;
    padding: 20px 20px;
    margin: 0 auto;
}

.payment-details-content {
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}

.payment-details-header {
    align-self: stretch;
    text-align: center;
    margin-bottom: 16px;
}

.payment-details-header span {
    font-size: 24px;
    font-weight: bold;
    text-transform: uppercase;
    word-wrap: break-word;
}

.payment-details-header .dark {
    color: #102649;
}

.payment-details-header .light {
    color: #007DB6;
}

.payment-details-greeting {
    width: 100%;
    max-width: 500px;
    text-align: center;
}

.payment-details-greeting .greeting-light {
    color: #007DB6;
    font-size: 16px;
    font-weight: 350;
    line-height: 32px;
    word-wrap: break-word;
}

.payment-details-greeting .greeting-dark {
    color: #102649;
    font-size: 16px;
    font-weight: 350;
    line-height: 32px;
    word-wrap: break-word;
}

.ticket-info {
    width: 100%;
    max-width: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 16px;
}

.ticket-info-row {
    align-self: stretch;
    text-align: center;
}

.ticket-info-label {
    color: #102649;
    font-size: 20px;
    font-weight: 350;
    line-height: 24px;
    word-wrap: break-word;
}

.ticket-info-value {
    color: #007DB6;
    font-size: 20px;
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
}

.payment-button-container {
    width: 100%;
    max-width: 260px;
    padding: 8px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;
}

.payment-button {
    align-self: stretch;
    height: 54px;
    position: relative;
    background: #F7C100;
    overflow: hidden;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    font-size: 16px;
    font-weight: bold;
    word-wrap: break-word;
}

.payment-button:hover {
    background: #e0af00;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.payment-button-text {
    text-align: center;
    color: #102649;
    font-size: 16px;
    font-weight: bold;
    word-wrap: break-word;
}

.payment-notes {
    align-self: stretch;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 16px;
}

.payment-note {
    text-align: center;
    color: #102649;
    font-size: 14px;
    font-weight: 350;
    line-height: 24px;
    word-wrap: break-word;
}

.payment-status-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
    margin-top: 30px;
}

.payment-info-section {
    width: 100%;
    max-width: 800px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
}

.payment-info-header {
    width: 100%;
    color: #102649;
    font-size: 16px;
    font-weight: bold;
    text-transform: uppercase;
    word-wrap: break-word;
    margin-bottom: 16px;
}

.payment-status-section {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
}

.payment-status-label {
    color: #007DB6;
    font-size: 14px;
    font-weight: 400;
    word-wrap: break-word;
    margin-right: 20px;
    margin-bottom: 10px;
}

.payment-status-value {
    color: #102649;
    font-size: 14px;
    font-weight: 350;
    word-wrap: break-word;
    margin-bottom: 10px;
}

.confirmation-number-label {
    color: #007DB6;
    font-size: 14px;
    font-weight: 400;
    word-wrap: break-word;
    margin-right: 20px;
    margin-bottom: 10px;
}

.confirmation-number-value {
    color: #102649;
    font-size: 14px;
    font-weight: 350;
    word-wrap: break-word;
}

.verification-reminder {
    background-color: #fff8e1;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #F7C100;
    margin: 15px auto;
    max-width: 90%;
}

/* Responsive design */
@media (max-width: 768px) {
    .ticket-wrapper {
        margin: 20px;
        max-width: none;
    }

    .ticket-card {
        padding: 30px 20px;
    }

    .ticket-title {
        font-size: 28px;
    }

    .agenda-button {
        padding: 12px 30px;
        font-size: 14px;
    }

    .ticket-container {
        padding: 0 20px 20px;
    }

    .status-container {
        flex-direction: column;
    }

    .status-item {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .payment-details-container {
        padding: 20px 10px;
    }

    .payment-details-header span {
        font-size: 20px;
    }

    .payment-info-section {
        flex-direction: column;
    }
    .ticket-benefits{gap:12px;}
    .ticket-benefits li{font-size:16px;}
}

/* Add subtle animation */
.ticket-card {
    animation: slideUp 0.6s ease-out;
}

.payment-details-container {
    animation: fadeIn 0.6s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
