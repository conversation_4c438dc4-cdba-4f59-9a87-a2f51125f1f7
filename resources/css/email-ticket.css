* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
}

a[x-apple-data-detectors] {
    color: inherit !important;
    text-decoration: inherit !important;
}

#MessageViewBody a {
    color: inherit;
    text-decoration: none;
}

p {
    line-height: inherit
}

.desktop_hide,
.desktop_hide table {
    mso-hide: all;
    display: none;
    max-height: 0px;
    overflow: hidden;
}

.image_block img+div {
    display: none;
}

@media (max-width:700px) {
    .desktop_hide table.icons-inner {
        display: inline-block !important;
    }

    .icons-inner {
        text-align: center;
    }

    .icons-inner td {
        margin: 0 auto;
    }

    .image_block img.fullWidth {
        max-width: 100% !important;
    }

    .social_block.desktop_hide .social-table {
        display: inline-block !important;
    }

    .row-content {
        width: 100% !important;
    }

    .stack .column {
        width: 100%;
        display: block;
    }

    .mobile_hide {
        max-width: 0;
        min-height: 0;
        max-height: 0;
        font-size: 0;
        display: none;
        overflow: hidden;
    }

    .desktop_hide,
    .desktop_hide table {
        max-height: none !important;
        display: table !important;
    }
}
