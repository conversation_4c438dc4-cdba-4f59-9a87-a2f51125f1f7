:root {
    --primary: #102649;
    --secondary: #F7C100;
}

body {
    font-family: 'Gotham Narrow', 'Inter', sans-serif;
}

.bg-primary {
    background-color: var(--primary) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.text-warning {
    color: var(--secondary) !important;
}

.btn-warning {
    background-color: var(--secondary);
    color: var(--primary);
    border: none;
}

.btn-warning:hover {
    background-color: #eeb800;
    color: var(--primary);
}

footer ul li a:hover {
    text-decoration: underline;
    color: var(--secondary) !important;
}

/* Logo styles */
.navbar-brand img {
    height: 38px;
}

.bg-primary img[alt="AFP Logo"] {
    height: 48px;
}

footer img {
    height: 38px;
}
