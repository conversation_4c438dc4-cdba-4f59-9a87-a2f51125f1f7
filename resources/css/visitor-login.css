/* Visitor Login Styles */

/* Global font styles */
body, html, h1, h2, h3, h4, h5, h6, p, a, span, div, ul, ol, li, input, button, textarea, select, label, table, th, td, tr, form, nav, header, footer, section, article, aside, main {
    font-family: 'Gotham Narrow', Arial, sans-serif !important;
}

/* Main container */
.visitor-login-container {
    width: 100%;
    height: 100%;
    padding: 45px 32px;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}

/* Header container */
.login-header {
    width: 100%;
    max-width: 693px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
}

/* Responsive styles for login container */
@media (max-width: 768px) {
    .visitor-login-container {
        padding: 30px 20px;
    }
}

@media (max-width: 480px) {
    .visitor-login-container {
        padding: 20px 16px;
    }
}

/* Title container */
.login-title-container {
    padding: 10px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

/* Title styling */
.login-title-primary {
    color: #007DB6;
    font-size: 24px;
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
    text-align: center;
}

.login-title-secondary {
    color: #102649;
    font-size: 24px;
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
    text-align: center;
}

/* Subtitle container */
.login-subtitle-container {
    align-self: stretch;
    padding: 10px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    width: 100%;
}

/* Subtitle text */
.login-subtitle {
    color: #102649;
    font-size: 16px;
    font-weight: 325;
    word-wrap: break-word;
    text-align: center;
}

/* Responsive text styles */
@media (max-width: 768px) {
    .login-title-primary,
    .login-title-secondary {
        font-size: 22px;
    }
    
    .login-subtitle {
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .login-title-primary,
    .login-title-secondary {
        font-size: 20px;
    }
    
    .login-subtitle {
        font-size: 14px;
    }
}

/* Alert styles */
.alert {
    width: 100%;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 12px;
    font-size: 16px;
    text-align: center;
    position: relative;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: #28a745;
    border: 1px solid #28a745;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
    border: 1px solid #dc3545;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.15);
    animation: fadeInAlert 0.3s ease-in-out;
}

@keyframes fadeInAlert {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form container */
.login-form-container {
    padding: 40px 56px;
    box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    border-radius: 24px;
    outline: 1px #F8F9FA solid;
    outline-offset: -1px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    background-color: #ffffff;
    width: 640px;
    max-width: 100%;
    transition: all 0.3s ease;
}

.login-form-container.has-error {
    border-top: 4px solid #dc3545;
}

/* Form inner container */
.login-form-inner {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 18px;
    width: 100%;
}

/* Form fields container */
.login-form-fields {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
    width: 100%;
}

/* Input field container */
.input-field-container {
    width: 528px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
    max-width: 100%;
}

/* Label container */
.input-label-container {
    align-self: stretch;
    height: 27px;
    position: relative;
    margin-bottom: 5px;
}

/* Label text */
.input-label {
    position: absolute;
    left: 0;
    top: 0;
    color: #102649;
    font-size: 16px;
    font-weight: 350;
    word-wrap: break-word;
}

/* Input field */
.input-field {
    align-self: stretch;
    height: 56px;
    position: relative;
    border-radius: 12px;
    outline: 1px rgba(16, 38, 73, 0.40) solid;
    outline-offset: -1px;
    width: 100%;
    transition: all 0.3s ease;
}

.has-error .input-field {
    outline: 1px #dc3545 solid;
    outline-offset: -1px;
    background-color: rgba(220, 53, 69, 0.03);
}

/* Form control */
.form-control {
    width: 100%;
    height: 100%;
    border: none;
    padding-left: 24px;
    padding-right: 24px;
    outline: none;
    font-size: 16px;
    color: #102649;
    background-color: transparent;
    border-radius: 12px;
}

/* Form control in password field wrapper */
.password-field-wrapper .form-control {
    padding-right: 60px; /* Make room for toggle button */
}

.form-control::placeholder {
    color: rgba(102, 102, 102, 0.60);
}

.has-error .form-control::placeholder {
    color: rgba(220, 53, 69, 0.6);
}

/* Password field wrapper */
.password-field-wrapper {
    position: relative;
}

/* Password toggle button */
.password-toggle-btn {
    position: absolute;
    right: 16px;
    top: 10px;
    background: none;
    border: none;
    color: #102649;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
}

.password-toggle-btn:hover {
    background-color: rgba(247, 193, 0, 0.1);
    color: #F7C100;
}

.password-toggle-btn:focus {
    outline: 2px solid #F7C100;
    outline-offset: 2px;
}

.password-toggle-btn:active {
    background-color: rgba(247, 193, 0, 0.2);
}

/* Bootstrap icons in password toggle */
.password-toggle-btn i {
    font-size: 18px;
    position: absolute;
}

.password-toggle-btn:hover i {
    color: #F7C100;
}

/* Error message */
.text-danger {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
    display: block;
}

/* Button container */
.button-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    width: 100%;
    margin-top: 10px;
}

/* Submit button */
.submit-button {
    width: 528px;
    height: 64px;
    position: relative;
    background: #102649;
    overflow: hidden;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    max-width: 100%;
    transition: background-color 0.3s ease;
}

.submit-button:hover {
    background: #1a3a6d;
}

.submit-button:disabled {
    background: #6c757d;
    cursor: not-allowed;
    opacity: 0.7;
}

.submit-button:disabled:hover {
    background: #6c757d;
}

/* Button text container */
.button-text-container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
}

/* Button text */
.button-text {
    text-align: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    font-weight: 500;
    word-wrap: break-word;
}

/* Remember me container */
.remember-me-container {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
    margin-top: 16px;
    width: 100%;
}

/* Custom checkbox wrapper */
.custom-checkbox-wrapper {
    display: flex;
    align-items: center;
    cursor: pointer;
}

/* Custom checkbox input (hidden) */
.custom-checkbox-input {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Custom checkbox label */
.custom-checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
}

/* Custom checkbox box */
.custom-checkbox-box {
    width: 20px;
    height: 20px;
    border: 2px solid #102649;
    border-radius: 4px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

/* Custom checkbox icon (checkmark) */
.custom-checkbox-icon {
    width: 14px;
    height: 14px;
    fill: white;
    display: none;
}

/* Show checkmark when checkbox is checked */
.custom-checkbox-input:checked + .custom-checkbox-label .custom-checkbox-box {
    background-color: #102649;
    border-color: #102649;
}

.custom-checkbox-input:checked + .custom-checkbox-label .custom-checkbox-icon {
    display: block;
}

/* Checkbox text */
.checkbox-text {
    color: #102649;
    font-size: 14px;
    font-weight: 325;
}

/* Hover effect for checkbox */
.custom-checkbox-label:hover .custom-checkbox-box {
    border-color: #F7C100;
    box-shadow: 0 0 0 2px rgba(247, 193, 0, 0.2);
}

/* Terms container */
.terms-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 24px;
}

/* Terms text */
.terms-text {
    color: #102649;
    font-size: 14px;
    font-weight: 325;
    word-wrap: break-word;
}

/* Terms link */
.terms-link {
    color: #102649;
    font-size: 14px;
    font-weight: 500;
    text-decoration: underline;
    word-wrap: break-word;
    transition: color 0.3s ease;
}

.terms-link:hover {
    color: #F7C100;
}

/* Forgot password container */
.forgot-password-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 10px;
    margin-top: 24px;
}

/* Forgot password link */
.forgot-password-link {
    color: #102649;
    font-size: 14px;
    font-weight: 500;
    text-decoration: underline;
    word-wrap: break-word;
    transition: color 0.3s ease;
}

.forgot-password-link:hover {
    color: #F7C100;
}

/* Tabs container */
.tabs-container {
    margin-bottom: 24px;
}

/* Responsive styles for auth tabs */
@media (max-width: 768px) {
    .auth-tabs-container {
        width: 100%;
    }
    
    .auth-tabs-container .row {
        width: 100% !important;
        margin: 0 auto;
    }
    
    .auth-tabs-container .btn {
        padding: 14px 4px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .auth-tabs-container .btn {
        padding: 12px 4px;
        font-size: 13px;
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* Back to login link container */
.back-link-container {
    width: 528px;
    height: 24px;
    position: relative;
    margin-top: 10px;
    max-width: 100%;
}

/* Back link wrapper */
.back-link-wrapper {
    position: absolute;
    left: 50%;
    top: 0.50px;
    transform: translateX(-50%);
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    gap: 10px;
}

/* Back arrow icon */
.back-arrow {
    width: 15px;
    height: 15px;
    position: relative;
    transform: rotate(180deg);
    transform-origin: top left;
}

.back-arrow-inner {
    width: 14px;
    height: 10.99px;
    position: absolute;
    left: 0.50px;
    top: 2px;
    background: #007DB6;
}

/* Back link text */
.back-link-text {
    text-align: right;
    color: #007DB6;
    font-size: 16px;
    font-weight: 400;
    word-wrap: break-word;
    text-decoration: none;
    transition: color 0.3s ease;
}

.back-link-text:hover {
    color: #005d8a;
    text-decoration: underline;
}

/* Password field styles */
.password-field-row {
    width: 528px;
    justify-content: center;
    align-items: center;
    gap: 392px;
    display: inline-flex;
}

.password-label {
    color: #102649;
    font-size: 16px;
    font-weight: 350;
    word-wrap: break-word;
}

.password-visibility-toggle {
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    display: flex;
    cursor: pointer;
}

.password-visibility-icon {
    width: 24px;
    height: 24px;
    position: relative;
    overflow: hidden;
}

.password-visibility-icon-inner1 {
    width: 17.10px;
    height: 16px;
    left: 2.91px;
    top: 4.01px;
    position: absolute;
    background: rgba(16, 38, 73, 0.40);
}

.password-visibility-icon-inner2 {
    width: 11.30px;
    height: 9.23px;
    left: 9.80px;
    top: 8.75px;
    position: absolute;
    background: rgba(16, 38, 73, 0.40);
}

.password-visibility-text {
    text-align: right;
    color: rgba(16, 38, 73, 0.40);
    font-size: 18px;
    font-weight: 350;
    word-wrap: break-word;
}

/* Responsive styles */
@media (max-width: 768px) {
    .login-header {
        width: 100%;
    }

    .alert {
        width: 100%;
    }

    .login-form-container {
        padding: 30px 20px;
    }

    .input-field-container,
    .submit-button,
    .back-link-container,
    .password-field-row,
    .forgot-password-container,
    .remember-me-container {
        width: 100%;
    }

    .password-field-row {
        gap: 20px;
        justify-content: space-between;
    }

    .back-link-wrapper,
    .forgot-password-link {
        width: 100%;
        display: flex;
        justify-content: center;
        left: 0;
    }
}
