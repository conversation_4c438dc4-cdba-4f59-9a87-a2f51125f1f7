/* Visitor Reset Password Styles */

/* Page container */
.verify-password-page {
    width: 100%;
    height: 100%;
    position: relative;
    background: white;
    overflow: hidden;
}

/* Header section */
.verify-header {
    width: 1280px;
    height: 76px;
    left: 0px;
    top: 0px;
    position: absolute;
    background: white;
    overflow: hidden;
}

.verify-header-content {
    width: 1062.58px;
    left: 110px;
    top: 8px;
    position: absolute;
    justify-content: space-between;
    align-items: center;
    display: inline-flex;
}

.verify-logo-container {
    width: 125.83px;
    height: 60px;
    position: relative;
}

.verify-logo {
    width: 125.83px;
    height: 50px;
    left: -5px;
    top: 5px;
    position: absolute;
}

.verify-nav {
    justify-content: flex-start;
    align-items: center;
    gap: 12px;
    display: flex;
}

.verify-nav-item {
    height: 40px;
    position: relative;
}

.verify-nav-about {
    width: 58.95px;
}

.verify-nav-programs {
    width: 81.98px;
}

.verify-nav-corporate {
    width: 143.78px;
}

.verify-nav-more {
    width: 66.59px;
}

.verify-nav-contact {
    width: 81.28px;
    background: #102649;
}

.verify-nav-text {
    left: 8px;
    top: 8px;
    position: absolute;
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: rgba(0, 0, 0, 0.65);
    font-family: 'Gotham Narrow';
    font-weight: 325;
    line-height: 24px;
    word-wrap: break-word;
}

.verify-nav-about .verify-nav-text {
    width: 43.15px;
    height: 24px;
    font-size: 15.88px;
}

.verify-nav-programs .verify-nav-text {
    width: 66.18px;
    height: 24px;
    font-size: 15.38px;
}

.verify-nav-corporate .verify-nav-text {
    width: 127.98px;
    height: 24px;
    font-size: 15.12px;
}

.verify-nav-more .verify-nav-text {
    width: 38.72px;
    height: 24px;
    font-size: 16px;
}

.verify-nav-contact .verify-nav-text {
    width: 55.48px;
    height: 24px;
    left: 13px;
    top: 7px;
    text-align: center;
    color: white;
    font-size: 14px;
    font-family: 'Inter';
    font-weight: 700;
}

.verify-nav-more-arrow {
    width: 8px;
    height: 4px;
    left: 50.59px;
    top: 16.92px;
    position: absolute;
    border-left: 4px black solid;
    border-top: 4px black solid;
    border-right: 4px black solid;
}

/* Event banner */
.verify-event-banner {
    width: 1280px;
    height: 80px;
    left: 0px;
    top: 76px;
    position: absolute;
    background: #102649;
}

.verify-event-content {
    width: 974px;
    height: 71px;
    left: 103px;
    top: 4px;
    position: absolute;
}

.verify-event-logo {
    width: 142px;
    height: 71px;
    left: 0px;
    top: 0px;
    position: absolute;
}

.verify-event-info {
    padding-top: 1px;
    left: 170px;
    top: 11px;
    position: absolute;
    justify-content: center;
    align-items: center;
    gap: 227px;
    display: inline-flex;
}

.verify-event-title {
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: white;
    font-size: 24px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    text-transform: uppercase;
    line-height: 48px;
    word-wrap: break-word;
}

.verify-countdown {
    width: 290px;
    height: 48px;
    position: relative;
}

.verify-countdown-container {
    width: 290px;
    height: 24px;
    left: 0px;
    top: 24px;
    position: absolute;
}

.verify-countdown-items {
    left: 3px;
    top: -24px;
    position: absolute;
    justify-content: center;
    align-items: center;
    gap: 46px;
    display: inline-flex;
}

.verify-countdown-item {
    flex-direction: column;
    justify-content: flex-start;
    display: inline-flex;
}

.verify-countdown-days {
    width: 30px;
    align-items: flex-start;
}

.verify-countdown-hours {
    width: 37px;
    align-items: center;
}

.verify-countdown-minutes {
    width: 38px;
    align-items: flex-start;
}

.verify-countdown-seconds {
    width: 42px;
    height: 48px;
    position: relative;
}

.verify-countdown-seconds-inner {
    width: 42px;
    left: 0px;
    top: 0px;
    position: absolute;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    display: inline-flex;
}

.verify-countdown-number {
    align-self: stretch;
    color: white;
    font-size: 24px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
}

.verify-countdown-minutes .verify-countdown-number {
    text-align: center;
}

.verify-countdown-label {
    align-self: stretch;
    color: white;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    line-height: 24px;
    word-wrap: break-word;
}

.verify-countdown-days .verify-countdown-label,
.verify-countdown-minutes .verify-countdown-label,
.verify-countdown-seconds .verify-countdown-label {
    font-size: 11px;
}

.verify-countdown-hours .verify-countdown-label {
    font-size: 12px;
}

/* Main container */
.reset-password-container {
    width: 100%;
    height: 100%;
    padding: 45px 32px;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}

/* Header container */
.reset-header-container {
    width: 693px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    display: flex;
}

/* Header padding */
.reset-header-padding {
    padding: 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

/* Title styling */
.reset-title-blue {
    color: #007DB6;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
}

.reset-title-dark {
    color: #102649;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
}

/* Subtitle container */
.reset-subtitle-container {
    align-self: stretch;
    padding: 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

/* Subtitle text */
.reset-subtitle-text {
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 325;
    word-wrap: break-word;
}

/* Form container */
.reset-form-container {
    padding-left: 56px;
    padding-right: 56px;
    padding-top: 40px;
    padding-bottom: 40px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 24px;
    outline: 1px #F8F9FA solid;
    outline-offset: -1px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: flex;
}

/* Form inner container */
.reset-form-inner {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 18px;
    display: flex;
}

/* Form field container */
.reset-form-field-container {
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
    display: flex;
}

/* Form field */
.reset-form-field {
    width: 528px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    align-items: flex-start;
    gap: 4px;
}

/* Form label container */
.reset-form-label-container {
    align-self: stretch;
    height: 27px;
    position: relative;
}

/* Form label */
.reset-form-label {
    position: absolute;
    left: 0px;
    top: 0px;
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 350;
    word-wrap: break-word;
}

/* Input container */
.reset-input-container {
    align-self: stretch;
    height: 56px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    outline: 1px rgba(16, 38, 73, 0.40) solid;
    outline-offset: -1px;
}

/* Input placeholder */
.reset-input-placeholder {
    position: absolute;
    left: 24px;
    top: 15px;
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
}

/* Placeholder text */
.reset-placeholder-text {
    color: rgba(102, 102, 102, 0.60);
    font-size: 16px;
    font-family: 'Poppins';
    font-weight: 400;
    word-wrap: break-word;
}

/* Button container */
.reset-button-container {
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    display: flex;
}

/* Button */
.reset-button {
    width: 528px;
    height: 64px;
    background: #102649;
    overflow: hidden;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    position: relative;
    color: white;
}

/* Button text container */
.reset-button-text-container {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    color: white;
}

/* Button text */
.reset-button-text {
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: white !important;
    font-size: 22px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    word-wrap: break-word;
}

/* Add specific styling for spans inside buttons */
.reset-button span {
    color: white !important;
}

/* Back to login container */
.reset-back-container {
    width: 528px;
    height: 24px;
    position: relative;
}

/* Back to login link container */
.reset-back-link-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    margin-top: 10px;
    cursor: pointer;
}

/* Back arrow icon */
.reset-back-arrow {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-top: 2px solid #007DB6;
    border-right: 2px solid #007DB6;
    transform: rotate(220deg);
    flex-shrink: 0;
}

/* Back to login text */
.reset-back-text {
    text-align: center;
    color: #007DB6;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    word-wrap: break-word;
}

/* Verify New Password Page Styles */
.verify-password-page {
    width: 100%;
    height: 100%;
}

.verify-code-inputs {
    display: inline-flex;
    justify-content: center;
    align-items: flex-start;
    gap: 12px;
    margin: 8px 0;
}

.verify-code-input-container {
    width: 80px;
    border-radius: 8px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 10px;
    display: inline-flex;
}

.verify-code-input {
    align-self: stretch;
    height: 80px;
    padding: 8px;
    background: white;
    box-shadow: 0px 1px 2px rgba(16, 24, 40, 0.05);
    border-radius: 8px;
    outline: 1px #007DB6 solid;
    outline-offset: -1px;
    justify-content: flex-start;
    align-items: center;
    gap: 8px;
    display: inline-flex;
}

.verify-code-input-inner {
    flex: 1 1 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.verify-code-input-number {
    flex: 1 1 0;
    text-align: center;
    color: #102649;
    font-size: 48px;
    font-family: Inter, sans-serif;
    font-weight: 500;
    line-height: 60px;
    word-wrap: break-word;
    width: 60px;
    border: none;
    outline: none;
}

.verify-code-resend {
    align-self: stretch;
    text-align: center;
    margin: 16px 0;
}

.verify-code-resend-text {
    color: #102649;
    font-size: 14px;
    font-family: 'Gotham Narrow';
    font-weight: 325;
    line-height: 20px;
    word-wrap: break-word;
}

.verify-code-resend-link {
    color: #007DB6;
    font-size: 14px;
    font-family: 'Gotham Narrow';
    font-weight: 325;
    text-decoration: underline;
    line-height: 20px;
    word-wrap: break-word;
    cursor: pointer;
}

.verify-button-container {
    display: inline-flex;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
    margin-top: 16px;
}

.verify-button {
    width: 361px;
    height: 64px;
    position: relative;
    background: #007DB6;
    overflow: hidden;
    border-radius: 4px;
    border: none;
    cursor: pointer;
}

.verify-button-text {
    position: absolute;
    left: 152px;
    top: 18.50px;
    text-align: center;
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: white;
    font-size: 22px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    word-wrap: break-word;
}

/* Back to login link styles */
.reset-back-container {
    width: 528px;
    height: 24px;
    position: relative;
}

.reset-back-link-container {
    display: flex !important;
    align-items: center !important;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    cursor: pointer;
}

.reset-back-arrow {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-top: 2px solid #007DB6;
    border-right: 2px solid #007DB6;
    transform: rotate(220deg);
    flex-shrink: 0;
}

.reset-back-text {
    text-align: right;
    color: #007DB6;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    word-wrap: break-word;
}

/* Create New Password Page Styles */
.create-password-page {
    width: 100%;
    height: 100%;
    padding-left: 32px;
    padding-right: 32px;
    padding-top: 45px;
    padding-bottom: 45px;
    display: inline-flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
}

.reset-header-container {
    width: 693px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    display: flex;
}

.reset-header-padding {
    padding: 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

.reset-title-blue {
    color: #007DB6;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
}

.reset-title-dark {
    color: #102649;
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    word-wrap: break-word;
}

.reset-subtitle-container {
    align-self: stretch;
    padding: 10px;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

.reset-subtitle-text {
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 325;
    word-wrap: break-word;
}

.reset-form-container {
    padding-left: 56px;
    padding-right: 56px;
    padding-top: 40px;
    padding-bottom: 40px;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    overflow: hidden;
    border-radius: 24px;
    outline: 1px #F8F9FA solid;
    outline-offset: -1px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: flex;
}

.reset-form-inner {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 18px;
    display: flex;
}

.reset-form-field-container {
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 8px;
    display: flex;
}

.create-password-field {
    width: 528px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    gap: 4px;
    display: flex;
    margin-bottom: 18px;
}

.create-password-label {
    align-self: stretch;
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
    word-wrap: break-word;
}

.create-password-input-container {
    align-self: stretch;
    height: 56px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
    outline: 1px rgba(16, 38, 73, 0.40) solid;
    outline-offset: -1px;
}

.create-password-input {
    width: 100%;
    height: 100%;
    padding-left: 24px;
    padding-right: 24px;
    border: none;
    outline: none;
    color: #102649;
    font-size: 16px;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
}

.create-password-input::placeholder {
    color: rgba(102, 102, 102, 0.60);
    font-size: 16px;
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    word-wrap: break-word;
}

.password-toggle-btn {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    border: none;
    cursor: pointer;
    color: #102649;
}

.reset-button-container {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;
    display: flex;
}

.reset-button {
    width: 528px;
    height: 64px;
    background: #102649;
    overflow: hidden;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    color: white;
    font-size: 22px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-align: center;
    word-wrap: break-word;
}

.text-danger {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
}

/* Password toggle button positioning */
.reset-form-field .input-field {
    position: relative;
}

.reset-form-field .password-toggle-btn {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
    background-color: transparent;
    border: none;
    padding: 0;
    margin: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.reset-form-field .password-toggle-btn i {
    font-size: 16px;
    color: #6c757d;
    transition: color 0.2s ease-in-out;
}

.reset-form-field .password-toggle-btn:hover i {
    color: #102649;
}

/* Responsive styles */
@media (max-width: 768px) {
    .reset-header-container {
        width: 100%;
    }

    .create-password-field,
    .reset-button {
        width: 100%;
    }

    .reset-form-container {
        padding-left: 20px;
        padding-right: 20px;
    }
}

@media (max-width: 480px) {
    .verify-code-input-container {
        width: 60px;
    }

    .verify-code-input {
        height: 60px;
    }

    .verify-code-input-number {
        font-size: 32px;
        line-height: 40px;
    }
}

/* Responsive Design Media Queries */
@media (max-width: 768px) {
    /* Main container adjustments */
    .reset-password-container {
        padding: 30px 20px;
    }

    /* Header container width adjustment */
    .reset-header-container {
        width: 100%;
        max-width: 693px;
    }

    /* Form field width adjustment */
    .reset-form-field {
        width: 100%;
        max-width: 528px;
    }

    /* Button width adjustment */
    .reset-button {
        width: 100%;
        max-width: 528px;
    }

    /* Back to login container width adjustment */
    .reset-back-container {
        width: 100%;
        max-width: 528px;
    }

    /* Create password field width adjustment */
    .create-password-field {
        width: 100%;
        max-width: 528px;
    }

    /* Form container padding adjustment */
    .reset-form-container {
        padding-left: 30px;
        padding-right: 30px;
        padding-top: 30px;
        padding-bottom: 30px;
    }

    /* Button container width adjustment */
    .reset-button-container {
        width: 100%;
        align-self: stretch;
    }
}

@media (max-width: 480px) {
    /* Main container adjustments for smaller screens */
    .reset-password-container {
        padding: 20px 15px;
    }

    /* Title text size adjustment */
    .reset-title-blue,
    .reset-title-dark {
        font-size: 20px;
    }

    /* Subtitle text size adjustment */
    .reset-subtitle-text {
        font-size: 14px;
    }

    /* Form container padding adjustment */
    .reset-form-container {
        padding-left: 20px;
        padding-right: 20px;
        padding-top: 25px;
        padding-bottom: 25px;
    }

    /* Button height adjustment */
    .reset-button {
        height: 56px;
    }

    /* Button text size adjustment */
    .reset-button-text {
        font-size: 16px;
    }

    /* Button container additional fixes */
    .reset-button-container {
        width: 100%;
    }

    /* Button additional fixes */
    .reset-button {
        width: 100%;
    }
}

/* Footer section */
.verify-footer {
    width: 1281px;
    left: -1px;
    top: 690px;
    position: absolute;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
}

.verify-footer-newsletter {
    align-self: stretch;
    height: 201px;
    padding: 28px 73px;
    background: #102649;
    justify-content: center;
    align-items: center;
    display: inline-flex;
}

.verify-newsletter-content {
    width: 531px;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 4px;
    display: inline-flex;
}

.verify-newsletter-title {
    color: #F7C100;
    font-size: 36px;
    font-family: 'Gotham Narrow';
    font-weight: 400;
    text-transform: uppercase;
    line-height: 48px;
    word-wrap: break-word;
}

.verify-newsletter-subtitle {
    width: 483px;
    color: white;
    font-size: 16px;
    font-family: 'Gotham Narrow';
    font-weight: 350;
    line-height: 24px;
    word-wrap: break-word;
}

.verify-newsletter-form {
    width: 536px;
    height: 84px;
    position: relative;
    background: #FAFAFA;
    overflow: hidden;
    border-radius: 8px;
}

.verify-newsletter-input {
    left: 37px;
    top: 18px;
    position: absolute;
    color: #B2B2B2;
    font-size: 16px;
    font-family: 'Plus Jakarta Sans';
    font-weight: 400;
    line-height: 48px;
    word-wrap: break-word;
}

.verify-newsletter-button {
    width: 209.46px;
    height: 56px;
    padding: 16px;
    left: 309px;
    top: 14px;
    position: absolute;
    background: #F7C100;
    border-radius: 4px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    display: inline-flex;
}

.verify-newsletter-button-inner {
    justify-content: center;
    align-items: center;
    gap: 9px;
    display: inline-flex;
}

.verify-newsletter-button-text {
    text-align: right;
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: #102649;
    font-size: 18px;
    font-family: 'Plus Jakarta Sans';
    font-weight: 700;
    line-height: 24px;
    word-wrap: break-word;
}

.verify-footer-main {
    align-self: stretch;
    height: 332px;
    background: #102649;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    display: flex;
}

.verify-footer-content {
    width: 1066px;
    max-width: 1320px;
    padding: 48px 0;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 16px;
    display: flex;
}

.verify-footer-links {
    align-self: stretch;
    justify-content: center;
    align-items: flex-start;
    display: inline-flex;
    flex-wrap: wrap;
    align-content: flex-start;
}

.verify-footer-column {
    flex: 1 1 0;
    height: 163.18px;
    max-width: 1320px;
    padding: 0 12px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
}

.verify-footer-column-header {
    align-self: stretch;
    height: 27.19px;
    position: relative;
}

.verify-footer-column-title {
    width: 285px;
    padding-bottom: 0.59px;
    left: 0px;
    top: -0.60px;
    position: absolute;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
}

.verify-footer-title-text {
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: white;
    font-family: 'Inter';
    font-weight: 700;
    line-height: 19.20px;
    word-wrap: break-word;
}

.verify-footer-beacon-title {
    font-size: 14.62px;
}

.verify-footer-afp-title {
    font-size: 14.75px;
}

.verify-footer-more-title {
    font-size: 14.25px;
}

.verify-footer-column-links {
    align-self: stretch;
    padding-bottom: 16px;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: flex;
}

.verify-footer-links-container {
    align-self: stretch;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: flex;
}

.verify-footer-link-item {
    align-self: stretch;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    display: flex;
}

.verify-footer-link {
    justify-content: flex-start;
    align-items: flex-start;
    display: inline-flex;
}

.verify-footer-link-text {
    justify-content: center;
    display: flex;
    flex-direction: column;
    color: #F8F9FA;
    font-family: 'Inter';
    font-weight: 700;
    line-height: 24px;
    word-wrap: break-word;
}

/* Responsive styles */
@media (max-width: 768px) {
    .verify-code-inputs {
        gap: 8px;
    }

    .verify-code-input-container {
        width: 70px;
    }

    .verify-code-input {
        height: 70px;
    }

    .verify-code-input-number {
        font-size: 40px;
        line-height: 50px;
    }

    .verify-button-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
    }

    .verify-button {
        width: 100%;
        max-width: 361px;
        height: 56px;
    }

    .verify-button-text {
        position: static;
        font-size: 18px;
    }

    .reset-back-container {
        width: 100%;
    }

    .reset-back-link-container {
        position: relative;
        left: auto;
        display: flex;
        justify-content: center;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .verify-code-input-container {
        width: 60px;
    }

    .verify-code-input {
        height: 60px;
    }

    .verify-code-input-number {
        font-size: 32px;
        line-height: 40px;
    }

    .verify-button-container {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
    }

    .verify-button {
        width: 100%;
        max-width: 320px;
        height: 50px;
    }

    .verify-button-text {
        position: static;
        font-size: 16px;
    }
}

body, html, h1, h2, h3, h4, h5, h6, p, a, span, div, ul, ol, li, input, button, textarea, select, label, table, th, td, tr, form, nav, header, footer, section, article, aside, main {
    font-family: 'Gotham Narrow', Arial, sans-serif !important;
}
