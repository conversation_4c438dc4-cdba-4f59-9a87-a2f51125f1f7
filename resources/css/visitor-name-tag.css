/* Container and Layout */
.ticket-container {
    max-width: 1200px;
    margin: 0 auto;
}

.ticket-header {
    text-align: center;
    margin-bottom: 40px;
}

.ticket-header h1 {
    font-size: 24px;
    font-family: 'Gotham Narrow', sans-serif;
    text-transform: uppercase;
    margin-bottom: 10px;
}

.ticket-header h1::first-word {
    color: #007DB6;
}

.ticket-header p {
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
}

.ticket-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 60px;
    align-items: start;
}

/* Form Styles */
.form-section {
    width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 14px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
    margin-bottom: 8px;
}

.form-group select,
.form-group input {
    height: 45px;
    width: 100%;
    background: white;
    border: 1px solid rgba(16, 38, 73, 0.40);
    border-radius: 12px;
    padding: 0 14px;
    color: #102649;
    font-size: 14px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
}

.form-group input[readonly] {
    background: rgba(202, 208, 215, 0.42);
}

.form-group input::placeholder {
    color: rgba(16, 38, 73, 0.29);
}

/* Multi-select workshops styling */
.workshops-select {
    overflow-y: auto; /* Still useful if many options, though less common for single select */
}

.workshops-select option {
    padding: 6px 8px;
    white-space: nowrap; /* Prevent text from wrapping */
    overflow: hidden; /* Hide overflowing text */
    text-overflow: ellipsis; /* Add ellipsis to truncated text */
}

/* Phone Input */
.phone-input {
    display: flex;
    position: relative;
    align-items: center;
    border: 1px solid rgba(16, 38, 73, 0.40);
    border-radius: 12px;
    /*background: rgba(202, 208, 215, 0.42);*/
    overflow: hidden;
}

.phone-input .country-select-button {
    display: flex;
    align-items: center;
    padding: 0 10px;
    height: 45px;
    background: rgba(202, 208, 215, 0.42);
    border-right: 1px solid rgba(16, 38, 73, 0.20);
    min-width: 80px;
    justify-content: center;
}

.phone-input input {
    flex: 1;
    border: none;
    border-radius: 0;
    background: transparent;
}

.phone-input input:focus {
    outline: none;
}

/* Flag icons */
.fi {
    width: 1.2em;
    height: 1em;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

/* File Upload */
.file-upload {
    display: flex;
    align-items: center;
    gap: 16px;
    background: #F8F9FA;
    border-radius: 5px;
    padding: 10px;
    height: 45px;
}

.choose-file-btn {
    padding: 10px 20px;
    background: white;
    border: 1px solid #102649;
    border-radius: 5px;
    color: #102649;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.choose-file-btn:hover {
    background: #102649;
    color: white;
}

.file-name {
    color: #3C3C3C;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;
}

/* Update Button */
.update-btn {
    width: 254px;
    height: 45px;
    background: #007DB6;
    border: none;
    border-radius: 4px;
    color: white;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
    cursor: pointer;
    margin: 30px auto;
    display: block;
    transition: background 0.3s ease;
}

.update-btn:hover {
    background: #005a87;
}

/* Ticket Info Section */
.ticket-info {
    margin-top: 40px;
}

.ticket-info h3 {
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    text-transform: uppercase;
    margin-bottom: 16px;
}

.info-items-container {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    margin-top: 20px;
}

.info-item {
    margin-right: 30px;
    min-width: auto;
    flex: 0 0 auto;
}

.info-item:last-child {
    margin-right: 0;
}

.info-item label {
    color: #007DB6;
    font-size: 14px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 400;
    display: block;
    margin-bottom: 8px;
    white-space: nowrap;
}

.info-item p {
    color: #102649;
    font-size: 14px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
}

/* Ticket Preview */
.ticket-preview {
    overflow: hidden;
}

.visitor-name-tag .ticket-card {
    padding: 0;
    position: relative;
}

.visitor-name-tag .ticket-card img {
    width: 100%;
    height: auto;
    display: block;
}

.visitor-name-tag .speaker-info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(16, 38, 73, 0.9);
    color: white;
    padding: 20px;
    text-align: center;
}

.visitor-name-tag .speaker-info h3 {
    font-size: 20px;
    margin-bottom: 5px;
}

.visitor-name-tag .speaker-info p {
    font-size: 14px;
    margin-bottom: 5px;
}

/* Ticket Divider */
.ticket-divider {
    height: 40px;
    background: white;
    position: relative;
    overflow: hidden;
}

.ticket-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    transform: translateY(-50%);
}

/* Share Section */
.share-section {
    padding: 24px 0;
    text-align: center;
}

.share-linkedin-btn {
    background: #F7C100;
    border: none;
    border-radius: 4px;
    padding: 17px 30px;
    color: #102649;
    font-size: 16px;
    font-family: 'Gotham Narrow', sans-serif;
    font-weight: 350;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.share-linkedin-btn:hover {
    transform: translateY(-2px);
}

.share-linkedin-btn .icon {
    width: 20px;
    height: 20px;
    border: 1.5px solid #102649;
    display: inline-block;
}

/* Name Tag Styles */
.name-tag-image-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.name-tag-image {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.name-tag-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 15px;
    width: 100%;
}

.download-pdf-btn, .print-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 15px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.download-pdf-btn {
    background-color: #007DB6;
    color: white;
    border: none;
}

.download-pdf-btn:hover {
    background-color: #0069a3;
}

.print-btn {
    background-color: #102649;
    color: white;
    border: none;
}

.print-btn:hover {
    background-color: #0a1a36;
}

.download-pdf-btn .icon, .print-btn .icon {
    margin-right: 8px;
    width: 18px;
    height: 18px;
    background-size: contain;
    background-repeat: no-repeat;
}

.download-pdf-btn .icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4'/%3E%3C/svg%3E");
}

.print-btn .icon {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='white'%3E%3Cpath stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z'/%3E%3C/svg%3E");
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }
    .name-tag-image-container, .name-tag-image-container * {
        visibility: visible;
    }
    .name-tag-actions {
        display: none;
    }
    .name-tag-image-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 0;
        padding: 0;
    }
    .name-tag-image {
        max-width: 100%;
        max-height: 100%;
        margin: 0;
        box-shadow: none;
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .ticket-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .ticket-preview {
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .info-items-container {
        flex-direction: column;
        gap: 16px;
    }
    
    .info-item {
        width: 100%;
        max-width: 100%;
    }

    .ticket-container {
        padding: 20px;
    }
}

body, html, h1, h2, h3, h4, h5, h6, p, a, span, div, ul, ol, li, input, button, textarea, select, label, table, th, td, tr, form, nav, header, footer, section, article, aside, main {
    font-family: 'Gotham Narrow', Arial, sans-serif !important;
}
