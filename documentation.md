# Cairo Beacon Event Management System Documentation

## Project Overview

The Cairo Beacon Event Management System is a comprehensive Laravel-based web application designed to facilitate the management of professional events, conferences, and forums. The platform serves as a bridge between event organizers, sponsors, speakers, and attendees, providing tools for registration, ticket management, agenda planning, and event participation.

## Business Logic Architecture

### 1. Multi-Tenant Event Management

The system operates on a multi-tenant architecture, allowing multiple events to be managed through the same application. Each event has its own:

- Dedicated domain/subdomain
- Custom branding elements
- Event-specific content
- Unique set of tickets, agendas, and resources

The tenant middleware ensures that users only access data relevant to their specific event.

### 2. User Types and Authentication Systems

The application manages three primary user types with separate authentication systems:

#### 2.1 Admin Users
- Administrators who manage the backend system
- Full access to event configuration, visitor management, and reporting
- Secured through Laravel's default authentication system

#### 2.2 Visitors
- Event attendees who register through the frontend
- Custom authentication guard (`visitor`)
- Can purchase tickets, manage profiles, and participate in events
- Email verification required

#### 2.3 Speakers
- Distinguished visitors who present at events
- Managed through the admin panel with special designations

### 3. Event Registration and Ticket Management

#### 3.1 Visitor Registration
- Self-service registration form with profile details
- Corporate and individual registration options
- LinkedIn profile integration
- Support for image uploads for name tags/badges

#### 3.2 Ticket System
- Multiple ticket types (regular, VIP, corporate, etc.)
- Dynamic pricing with time-based discounts
- Payment processing integration
- Approval workflows for certain ticket types
- Corporate ticket allocation system (table booking)

#### 3.3 Payment Processing
- Integration with payment gateways
- Support for invoice generation
- Payment status tracking (pending, approved, completed)
- Discount code functionality

### 4. Event Content Management

#### 4.1 Agenda Management
- Multi-day event scheduling
- Workshop and session management
- Speaker assignments
- Attendee registration for specific sessions

#### 4.2 Sponsor Management
- Sponsor registration and approval workflow
- Multiple sponsorship tiers
- Logo and content management for sponsors
- Sponsor request handling

#### 4.3 Content Features
- Gallery management for event photos
- FAQ management
- Sliders and promotional content
- Social media integration

### 5. On-Site Event Management

#### 5.1 QR Code System
- QR code generation for tickets
- Attendance tracking through QR scanning
- Name tag generation and distribution tracking

#### 5.2 Corporate Tables
- Management of corporate table bookings
- Assignment of visitors to specific tables
- Corporate guest management

#### 5.3 Attendance Tracking
- Check-in/out functionality
- Reporting on attendance
- Not-attending status management

### 6. Post-Event Features

#### 6.1 Surveys
- Post-event survey creation and distribution
- Response collection and analysis

#### 6.2 Graduation Certificates
- Certificate generation for eligible participants
- Distribution management

## Technical Implementation Details

### Database Architecture

The application follows Laravel conventions with models representing core business entities:

- **Event**: The central entity representing individual events
- **Visitor**: Represents attendees with registration details
- **Ticket**: Defines different ticket types and pricing
- **VisitorTicket**: Junction model for ticket purchases
- **Agenda**: Event schedule and session information
- **AgendaSpeaker**: Links speakers to agenda items
- **Sponsor**: Companies supporting the event
- **Gallery**: Event media management
- **Faq**: Frequently asked questions
- **SalesActivity**: Tracking sales interactions
- **Survey**: Post-event feedback collection
- **Invoice**: Payment documentation

### Key Relationships

- Events have many tickets, agendas, sponsors, and content items
- Visitors can purchase multiple tickets
- Visitors can register for multiple agenda items
- Corporate visitors can invite others through a code system

### Authentication Flows

- Customized auth controllers for admin and visitor logins
- Password reset functionality for both user types
- Session management with appropriate guards

### Frontend Design

The application has two primary interfaces:

1. **Public Frontend**:
   - Event marketing pages
   - Registration and ticket purchasing
   - Visitor profile management
   - Schedule viewing

2. **Admin Backend**:
   - Comprehensive dashboard
   - Visitor management tools
   - Content management system
   - Reporting and analytics

## Operational Workflows

### Event Creation Process
1. Admin creates new event with domain and basic details
2. Event content (tickets, agenda, etc.) is configured
3. Event is published and becomes accessible to visitors

### Visitor Registration Flow
1. Visitor completes registration form
2. Email verification is requested
3. Profile is created
4. Optional: Corporate code validation
5. Ticket selection and purchase

### Ticket Purchase Process
1. Visitor selects desired ticket
2. Payment is processed
3. Admin approval (if required)
4. Ticket confirmation and QR code generation
5. Email notification with ticket details

### On-Site Check-in Process
1. Visitor arrives with QR code
2. Staff scans QR code
3. Attendance is recorded
4. Name tag is generated/distributed
5. Visitor is directed to appropriate area/table

## Integration Points

- Payment gateway for processing transactions
- Email service for notifications
- LinkedIn API for social authentication
- QR code generation and scanning services

## Security Considerations

- Separate authentication guards for different user types
- Password encryption using Laravel's built-in mechanisms
- Middleware protection for sensitive routes
- Validation on all form submissions
- Proper authorization checks throughout the application

## Conclusion

The Cairo Beacon Event Management System provides a comprehensive solution for planning, organizing, and executing professional events. The multi-tenant architecture allows for scalability, while the robust feature set ensures that all aspects of event management are handled efficiently.
