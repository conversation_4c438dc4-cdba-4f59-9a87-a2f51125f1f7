# Session Management System

This document describes the session management and automatic logout functionality implemented in the Cairo Beacon Event application.

## Features

### 1. Automatic Session Expiration
- Sessions automatically expire after the configured lifetime (default: 120 minutes)
- Users are automatically logged out when their session expires
- Expired users are redirected to the home page

### 2. Session Warning System
- Users receive a warning 5 minutes before session expiration
- Warning includes option to extend session or logout
- Uses SweetAlert2 for better user experience

### 3. Activity-Based Session Extension
- User activity (mouse, keyboard, scroll, touch) automatically extends the session
- Activity is debounced to prevent excessive server requests
- Only updates session for authenticated users

### 4. AJAX and Livewire Support
- Handles session expiration for AJAX requests
- Special handling for Livewire components
- Returns appropriate JSON responses with expiration indicators

## Implementation Details

### Middleware: `CheckSessionExpiration`
- **Location**: `app/Http/Middleware/CheckSessionExpiration.php`
- **Purpose**: Checks session expiration on each request
- **Features**:
  - Initializes session activity tracking
  - Validates session lifetime
  - Handles different response types (JSON, Livewire, regular)
  - Updates last activity time

### JavaScript Session Handler
- **Location**: `resources/views/components/layouts/app.blade.php`
- **Features**:
  - Client-side session timeout tracking
  - Activity monitoring and session extension
  - Warning dialogs before expiration
  - Automatic logout handling
  - AJAX/Livewire session expiration detection

### Routes
- **Session Extend**: `GET /extend-session` - Extends user session
- **Logout**: `POST /logout` - Logs out user and invalidates session

### Configuration
- **Session Lifetime**: Configured in `config/session.php` (default: 120 minutes)
- **Warning Time**: 5 minutes before expiration
- **Activity Debounce**: 1 second

## Usage

### For Authenticated Routes
The session expiration middleware is applied to all visitor routes:

```php
Route::prefix('visitor')
    ->name('visitor.')
    ->middleware(['auth:sanctum', 'session.expired'])
    ->group(function () {
        // Protected routes
    });
```

### Configuration Options
You can modify session behavior in your `.env` file:

```env
SESSION_LIFETIME=120  # Session lifetime in minutes
SESSION_DRIVER=database
SESSION_EXPIRE_ON_CLOSE=false
```

## User Experience

1. **Active Users**: Sessions extend automatically with user activity
2. **Inactive Users**: Receive warning 5 minutes before expiration
3. **Expired Sessions**: Automatically logged out with notification
4. **AJAX Operations**: Gracefully handle session expiration without errors

## Technical Notes

- Uses Laravel Sanctum for authentication
- Session data stored in database
- CSRF protection included
- Fallback alerts if SweetAlert2 unavailable
- Activity tracking prevents false timeouts
- Debounced activity to optimize performance

## Troubleshooting

### Session Not Extending
- Check if JavaScript is enabled
- Verify CSRF token is present
- Ensure `/extend-session` route is accessible

### Warnings Not Showing
- Check if SweetAlert2 is loaded
- Verify user is authenticated
- Check browser console for errors

### Immediate Logouts
- Check session configuration
- Verify database session storage is working
- Check if middleware is properly applied
